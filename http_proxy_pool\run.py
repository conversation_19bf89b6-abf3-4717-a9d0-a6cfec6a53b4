from http_proxy_pool.scheduler import Scheduler
import argparse


parser = argparse.ArgumentParser(description='HTTP Proxy Pool')
parser.add_argument('--processor', type=str, help='processor to run (getter, tester, server)')
args = parser.parse_args()

if __name__ == '__main__':
    # if processor set, just run it
    if args.processor:
        getattr(Scheduler(), f'run_{args.processor}')()
    else:
        Scheduler().run()
