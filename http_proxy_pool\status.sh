#!/bin/bash

# 协议感知代理池系统状态检查脚本

PROJECT_ROOT="D:\onedrive\proxy_pool\http_proxy_pool"
cd "$PROJECT_ROOT"

echo "📊 协议感知代理池系统状态"
echo "=" * 40

# 检查各个服务状态
for service in api getter tester; do
    if [ -f "pids/$service.pid" ]; then
        PID=$(cat "pids/$service.pid")
        if kill -0 $PID 2>/dev/null; then
            echo "✅ $service: 运行中 (PID: $PID)"
        else
            echo "❌ $service: 已停止"
        fi
    else
        echo "❌ $service: 未启动"
    fi
done

echo ""

# 检查API服务器
if curl -s http://localhost:5555/health > /dev/null 2>&1; then
    echo "✅ API服务器: 可访问"
    echo "📈 系统统计:"
    curl -s http://localhost:5555/stats | python3 -m json.tool 2>/dev/null || echo "无法获取统计信息"
else
    echo "❌ API服务器: 不可访问"
fi
