from attr import attrs, attr
import re
from typing import Optional, Dict, Any
from urllib.parse import urlparse, urlunparse
from http_proxy_pool.utils.protocol_detector import ProtocolDetector, ProtocolType, ProtocolStandardizer
from http_proxy_pool.exceptions import ProtocolDetectionException, ProtocolValidationException


@attrs(eq=False, hash=False)
class Proxy(object):
    """
    增强的代理模式类
    支持多种协议、认证信息和实用方法
    """
    host = attr(type=str, default=None)
    port = attr(type=int, default=None)
    protocol = attr(type=str, default='http')  # 支持 http, https, socks4, socks5
    username = attr(type=str, default=None)    # 用户名
    password = attr(type=str, default=None)    # 密码
    
    @classmethod
    def from_string(cls, proxy_string: str, source_protocol: Optional[ProtocolType] = None):
        """
        从代理字符串创建Proxy对象（协议感知）
        
        :param proxy_string: 代理字符串
        :param source_protocol: 来源协议（用于txt_proxies推断）
        :return: Proxy对象
        :raises ProtocolDetectionException: 协议检测失败时抛出
        """
        if not proxy_string:
            raise ProtocolDetectionException("Empty proxy string")
        
        # 尝试从字符串检测协议
        detected_protocol = ProtocolDetector.detect_protocol_from_string(proxy_string)
        
        # 解析组件
        host, port, username, password = ProtocolDetector.parse_proxy_components(proxy_string)
        
        if host is None or port is None:
            raise ProtocolDetectionException("Invalid proxy format", proxy_string)
        
        # 确定最终协议
        final_protocol = 'http'  # 默认协议
        if detected_protocol:
            final_protocol = detected_protocol.value
        elif source_protocol:
            final_protocol = source_protocol.value
        
        return cls(
            host=host,
            port=port,
            protocol=final_protocol,
            username=username,
            password=password
        )
    
    @classmethod
    def from_string_with_source_inference(cls, proxy_string: str, url_type: str):
        """
        从代理字符串创建Proxy对象，根据来源URL类型推断协议
        
        :param proxy_string: 代理字符串
        :param url_type: URL类型 ('http_urls', 'socks4_urls', 'socks5_urls')
        :return: Proxy对象
        :raises ProtocolDetectionException: 协议检测失败时抛出
        """
        try:
            source_protocol = ProtocolDetector.infer_protocol_from_source(url_type)
            return cls.from_string(proxy_string, source_protocol)
        except Exception as e:
            raise ProtocolDetectionException(f"Failed to create proxy from source: {e}", proxy_string)
    
    def __attrs_post_init__(self):
        """初始化后的处理"""
        # 如果host包含协议信息，解析它
        if self.host and '://' in self.host:
            self._parse_host_with_protocol()
        
        # 验证协议类型并标准化
        self._normalize_protocol()
        
        # 如果没有明确协议且host包含完整URL，尝试检测
        if self.protocol == 'http' and self.host and '://' in str(self.host):
            self._detect_protocol_from_host()
    
    def _normalize_protocol(self):
        """标准化协议类型"""
        if self.protocol not in ['http', 'https', 'socks4', 'socks5']:
            self.protocol = 'http'  # 默认为http
    
    def _detect_protocol_from_host(self):
        """从host字符串检测协议"""
        try:
            detected_protocol = ProtocolDetector.detect_protocol_from_string(str(self.host))
            if detected_protocol:
                self.protocol = detected_protocol.value
        except Exception:
            # 检测失败，保持默认协议
            pass
    
    def _parse_host_with_protocol(self):
        """解析包含协议的host字符串"""
        try:
            # 解析类似 "*********************" 的格式
            if '@' in self.host:
                # 包含认证信息
                parts = self.host.split('://', 1)
                if len(parts) == 2:
                    self.protocol = parts[0]
                    auth_host = parts[1]
                    if '@' in auth_host:
                        auth_part, host_part = auth_host.rsplit('@', 1)
                        if ':' in auth_part:
                            self.username, self.password = auth_part.split(':', 1)
                        else:
                            self.username = auth_part
                        self.host = host_part
            else:
                # 只有协议，没有认证信息
                parts = self.host.split('://', 1)
                if len(parts) == 2:
                    self.protocol = parts[0]
                    self.host = parts[1]
        except Exception:
            # 解析失败，保持原样
            pass
    
    def __str__(self):
        """
        基本字符串表示: host:port
        :return: 字符串格式的代理
        """
        return f'{self.host}:{self.port}'
    
    def string(self):
        """
        基本字符串表示 (向后兼容)
        :return: <host>:<port>
        """
        return self.__str__()
    
    def full_string(self):
        """
        完整字符串表示，包含协议和认证信息
        :return: 完整格式的代理字符串
        """
        if self.username and self.password:
            return f'{self.protocol}://{self.username}:{self.password}@{self.host}:{self.port}'
        elif self.username:
            return f'{self.protocol}://{self.username}@{self.host}:{self.port}'
        else:
            return f'{self.protocol}://{self.host}:{self.port}'
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        :return: 代理信息字典
        """
        return {
            'host': self.host,
            'port': self.port,
            'protocol': self.protocol,
            'username': self.username,
            'password': self.password,
            'full_string': self.full_string()
        }
    
    def to_requests_format(self) -> Dict[str, str]:
        """
        转换为requests库使用的代理格式
        :return: requests格式的代理字典
        """
        proxy_url = self.full_string()
        
        if self.protocol in ['http', 'https']:
            return {
                'http': proxy_url,
                'https': proxy_url
            }
        elif self.protocol in ['socks4', 'socks5']:
            return {
                'http': proxy_url,
                'https': proxy_url
            }
        else:
            return {
                'http': f'http://{self.host}:{self.port}',
                'https': f'http://{self.host}:{self.port}'
            }
    
    def to_curl_format(self) -> str:
        """
        转换为curl命令使用的代理格式
        :return: curl格式的代理字符串
        """
        if self.username and self.password:
            return f'--proxy {self.protocol}://{self.host}:{self.port} --proxy-user {self.username}:{self.password}'
        else:
            return f'--proxy {self.protocol}://{self.host}:{self.port}'
    
    def is_authenticated(self) -> bool:
        """
        检查代理是否需要认证
        :return: 是否需要认证
        """
        return bool(self.username)
    
    def is_secure(self) -> bool:
        """
        检查代理是否使用安全协议
        :return: 是否为安全协议
        """
        return self.protocol in ['https', 'socks5']
    
    def get_protocol_type(self) -> str:
        """
        获取协议类型的友好名称
        :return: 协议类型名称
        """
        protocol_names = {
            'http': 'HTTP',
            'https': 'HTTPS', 
            'socks4': 'SOCKS4',
            'socks5': 'SOCKS5'
        }
        return protocol_names.get(self.protocol, 'UNKNOWN')
    
    def get_protocol_enum(self) -> Optional[ProtocolType]:
        """
        获取协议类型枚举
        :return: ProtocolType枚举值
        """
        try:
            return ProtocolType(self.protocol)
        except ValueError:
            return None
    
    def get_redis_key(self, base_key: str = "proxies") -> str:
        """
        获取协议对应的Redis键
        :param base_key: 基础键名
        :return: 协议特定的Redis键
        """
        protocol_enum = self.get_protocol_enum()
        if protocol_enum:
            return ProtocolDetector.get_redis_key_for_protocol(protocol_enum, base_key)
        return f"{base_key}:http"  # 默认使用http键
    
    def get_normalized_string(self) -> str:
        """
        获取标准化的代理字符串（用于Redis存储）
        移除协议前缀，保留纯host:port格式
        :return: 标准化的代理字符串
        """
        return f"{self.host}:{self.port}"
    
    def get_full_url(self) -> str:
        """
        获取完整的代理URL（用于实际连接）
        :return: 完整的代理URL
        """
        return ProtocolStandardizer.standardize_for_usage(
            self.host, self.port, self.get_protocol_enum() or ProtocolType.HTTP,
            self.username, self.password
        )
    
    def validate(self) -> bool:
        """
        验证代理信息的基本有效性
        :return: 是否有效
        """
        # 检查必需字段
        if not self.host or not self.port:
            return False
        
        # 检查端口范围
        if not (1 <= self.port <= 65535):
            return False
        
        # 检查host格式（简单验证）
        if not re.match(r'^[a-zA-Z0-9.-]+$', self.host):
            return False
        
        # 检查协议
        if self.protocol not in ['http', 'https', 'socks4', 'socks5']:
            return False
        
        return True
    
    def __eq__(self, other):
        """
        比较两个代理是否相同
        """
        if not isinstance(other, Proxy):
            return False
        return (self.host == other.host and 
                self.port == other.port and 
                self.protocol == other.protocol and
                self.username == other.username and
                self.password == other.password)
    
    def __hash__(self):
        """
        计算代理的哈希值，用于去重
        """
        return hash((self.host, self.port, self.protocol, self.username, self.password))
    
    def __repr__(self):
        """
        开发者友好的字符串表示
        """
        return f'Proxy(host="{self.host}", port={self.port}, protocol="{self.protocol}", authenticated={self.is_authenticated()})'
