[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "http_proxy_pool"
version = "0.1.0"
description = "动态http代理池"
readme = "README.md"
authors = [
    {name = "yyh357", email = "<EMAIL>"},
]
requires-python = ">=3.10"
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]
dependencies = [
    "environs",
    "Flask",  # 保留Flask用于兼容性测试
    "fastapi>=0.104.0",  # FastAPI框架
    "uvicorn[standard]>=0.24.0",  # ASGI服务器
    "attrs",
    "retrying",
    "aiohttp",
    "requests",
    "loguru",
    "pyquery",
    "redis>=5.0.0",  # 支持异步的Redis客户端
    "lxml",
    "fake_headers",
    "python-multipart",  # FastAPI文件上传支持
    "httpx>=0.25.0",  # 异步HTTP客户端，用于测试
]

[tool.setuptools.packages.find]
where = ["."]
include = ["http_proxy_pool*"]
exclude = ["tests*", "examples*", "logs*", "*.egg-info*", "__pycache__*", ".env*"]