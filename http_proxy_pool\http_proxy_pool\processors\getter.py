import asyncio
from loguru import logger
from http_proxy_pool.storages.protocol_aware_redis_client import ProtocolAwareRedisClient
from http_proxy_pool.setting import PROXY_NUMBER_MIN_VALID
from http_proxy_pool.crawlers import __all__ as crawlers_cls

# 配置简洁的日志输出
logger.remove()
logger.add(lambda msg: print(msg.rstrip()), level="INFO", format="{message}")


class Getter:
    """代理获取器 - 协议感知版本"""

    def __init__(self):
        self.redis = ProtocolAwareRedisClient()
        self.crawlers = [crawler_cls() for crawler_cls in crawlers_cls]

    async def _get_valid_count(self):
        """获取有效代理数量"""
        stats = await self.redis.get_protocol_stats()
        return sum(stat.get('high_score', 0) for stat in stats.values())

    async def _crawl_all_proxies(self):
        """并发爬取所有代理并去重"""
        loop = asyncio.get_event_loop()
        tasks = []
        crawler_names = []
        
        for crawler in self.crawlers:
            # 获取爬虫文件名
            crawler_name = crawler.__class__.__module__.split('.')[-1]
            crawler_names.append(crawler_name)
            tasks.append(loop.run_in_executor(None, lambda c=crawler: list(c.crawl())))
        
        results = await asyncio.gather(*tasks)
        
        # 高效去重：使用set存储已见过的代理键，dict存储唯一代理
        seen_keys = set()  # 用于快速查重
        unique_proxies = []  # 存储去重后的代理
        protocol_stats = {'http': 0, 'socks4': 0, 'socks5': 0}
        
        total_original = 0
        total_dedup = 0
        
        # 记录每个爬虫去重后的数量
        crawler_dedup_counts = {}
        
        for crawler_name, proxies in zip(crawler_names, results):
            dedup_count = 0
            
            for proxy in proxies:
                if proxy:
                    # 使用标准化字符串作为去重键
                    proxy_key = proxy.get_normalized_string()
                    if proxy_key not in seen_keys:
                        seen_keys.add(proxy_key)
                        unique_proxies.append(proxy)
                        dedup_count += 1
            
            crawler_dedup_counts[crawler_name] = dedup_count
        
        # 输出简洁的统计信息
        for crawler_name, dedup_count in crawler_dedup_counts.items():
            logger.info(f'{crawler_name}获取共计：{dedup_count}个代理')
        
        return unique_proxies

    async def run(self):
        """主运行方法"""
        # 检查是否需要获取新代理
        valid_count = await self._get_valid_count()
        if valid_count >= PROXY_NUMBER_MIN_VALID:
            return

        # 爬取新代理
        new_proxies = await self._crawl_all_proxies()
        if not new_proxies:
            return

        # 按协议分类存储
        results = await self.redis.add_batch_by_protocol(new_proxies)
        total_added = sum(results.values())
        
        # 构建协议分布字符串
        protocol_details = []
        for key, count in results.items():
            if count > 0:
                # 从redis_key中提取协议名 (如 "proxies:http" -> "http")
                protocol = key.split(':')[-1]
                protocol_details.append(f'{protocol}:{count}')
        
        details_str = ', '.join(protocol_details) if protocol_details else '无新增'
        logger.info(f'代理池新增{total_added}个代理({details_str})')


async def main():
    """主函数"""
    getter = Getter()
    await getter.run()
    await getter.redis.close()


if __name__ == '__main__':
    asyncio.run(main())
