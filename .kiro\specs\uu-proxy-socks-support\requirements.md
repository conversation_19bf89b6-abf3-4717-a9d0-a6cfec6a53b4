# UU Proxy SOCKS 协议支持需求文档

## 介绍

为现有的 UU Proxy 爬虫增加对 SOCKS4 和 SOCKS5 协议的支持。当前爬虫只支持 HTTP 协议，需要扩展以支持多种代理协议类型，提高代理池的多样性和可用性。

## 需求

### 需求 1：SOCKS4 协议支持

**用户故事：** 作为代理池管理员，我希望能够从 UU Proxy API 获取 SOCKS4 协议的代理，以便为用户提供更多类型的代理选择。

#### 验收标准

1. WHEN 爬虫配置为获取 SOCKS4 代理 THEN 系统 SHALL 调用 UU Proxy API 并设置 schemes='socks4'
2. WHEN API 返回 SOCKS4 代理数据 THEN 系统 SHALL 正确解析并创建带有 socks4 协议标识的 Proxy 对象
3. WHEN 解析 SOCKS4 代理字符串 THEN 系统 SHALL 确保代理格式为 socks4://host:port

### 需求 2：SOCKS5 协议支持

**用户故事：** 作为代理池管理员，我希望能够从 UU Proxy API 获取 SOCKS5 协议的代理，以便支持需要认证的高级代理功能。

#### 验收标准

1. WHEN 爬虫配置为获取 SOCKS5 代理 THEN 系统 SHALL 调用 UU Proxy API 并设置 schemes='socks5'
2. WHEN API 返回 SOCKS5 代理数据 THEN 系统 SHALL 正确解析并创建带有 socks5 协议标识的 Proxy 对象
3. WHEN 解析 SOCKS5 代理字符串 THEN 系统 SHALL 确保代理格式为 socks5://host:port

### 需求 3：多协议轮询支持

**用户故事：** 作为代理池管理员，我希望爬虫能够轮询获取不同协议类型的代理，以便最大化代理池的多样性。

#### 验收标准

1. WHEN 爬虫运行时 THEN 系统 SHALL 依次获取 HTTP、SOCKS4 和 SOCKS5 协议的代理
2. WHEN 某个协议的 API 调用失败 THEN 系统 SHALL 继续尝试其他协议而不中断整个爬取过程
3. WHEN 所有协议都获取完成 THEN 系统 SHALL 记录每种协议获取到的代理数量

### 需求 4：配置灵活性

**用户故事：** 作为系统配置员，我希望能够灵活配置需要获取的协议类型，以便根据实际需求调整爬虫行为。

#### 验收标准

1. WHEN 配置文件指定启用的协议列表 THEN 系统 SHALL 只获取指定协议的代理
2. WHEN 未指定协议配置 THEN 系统 SHALL 默认获取所有支持的协议（HTTP、SOCKS4、SOCKS5）
3. WHEN 协议配置无效 THEN 系统 SHALL 记录警告并使用默认配置

### 需求 5：错误处理和日志

**用户故事：** 作为系统运维人员，我希望能够清楚地了解不同协议代理获取的状态和可能的错误，以便进行故障排查。

#### 验收标准

1. WHEN 特定协议的 API 调用失败 THEN 系统 SHALL 记录详细的错误信息包含协议类型
2. WHEN 代理解析失败 THEN 系统 SHALL 记录原始代理字符串和协议类型
3. WHEN 爬取完成 THEN 系统 SHALL 记录每种协议成功获取的代理数量统计