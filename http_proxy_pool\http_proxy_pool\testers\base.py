from http_proxy_pool.setting import TEST_DONT_SET_MAX_SCORE, PROXY_SCORE_INIT, PROXY_SCORE_MAX, PROXY_SCORE_MIN


class BaseTester:
    """
    代理测试器基类
    用于扩展特定网站或服务的代理测试
    """
    test_url = ""
    key = ""
    test_dont_set_max_score = TEST_DONT_SET_MAX_SCORE
    proxy_score_init = PROXY_SCORE_INIT
    proxy_score_max = PROXY_SCORE_MAX
    proxy_score_min = PROXY_SCORE_MIN

    def headers(self):
        """返回测试请求的HTTP头"""
        return {}

    def cookies(self):
        """返回测试请求的Cookie"""
        return {}

    async def parse(self, html, url, proxy, expr='{"code":0}'):
        """
        解析测试响应，判断代理是否有效
        :param html: 响应内容
        :param url: 测试URL
        :param proxy: 代理地址
        :param expr: 成功标识
        :return: True表示代理有效
        """
        return expr in html
