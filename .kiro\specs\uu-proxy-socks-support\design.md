# UU Proxy SOCKS 协议支持设计文档

## 概述

本设计文档描述了如何在现有的 UU Proxy 爬虫基础上增加对 SOCKS4 和 SOCKS5 协议的支持。设计采用最小化修改原则，通过扩展现有的 `UUProxyCrawler` 类来实现多协议支持。

## 架构

### 当前架构分析

现有的 UU Proxy 爬虫架构：
```
UUProxyCrawler (BaseCrawler)
├── get_proxies() - API调用函数
├── crawl() - 主爬取方法
└── 配置常量 (SCHEMES='http')
```

### 增强后架构

```
UUProxyCrawler (BaseCrawler)
├── get_proxies() - API调用函数 (保持不变)
├── crawl() - 增强的主爬取方法
├── _crawl_by_scheme() - 新增：按协议爬取方法
├── _get_enabled_schemes() - 新增：获取启用协议列表
└── 配置常量 (支持多协议)
```

## 组件和接口

### 1. 配置增强

```python
# 原有配置
SCHEMES = 'http'  # 单一协议

# 增强后配置
SUPPORTED_SCHEMES = ['http', 'socks4', 'socks5']  # 支持的协议列表
ENABLED_SCHEMES = ['http', 'socks4', 'socks5']    # 启用的协议列表（可配置）
```

### 2. 核心方法设计

#### 2.1 `_get_enabled_schemes()` 方法
```python
def _get_enabled_schemes(self) -> List[str]:
    """获取启用的协议列表"""
    # 返回配置的启用协议列表
    # 验证协议有效性
    # 提供默认值处理
```

#### 2.2 `_crawl_by_scheme(scheme: str)` 方法
```python
async def _crawl_by_scheme(self, scheme: str):
    """按指定协议爬取代理"""
    # 调用 get_proxies() 获取指定协议的代理
    # 解析返回的代理文本
    # 转换为 Proxy 对象并 yield
```

#### 2.3 增强的 `crawl()` 方法
```python
async def crawl(self):
    """主爬取方法 - 支持多协议"""
    # 获取启用的协议列表
    # 遍历每个协议调用 _crawl_by_scheme()
    # 统计每个协议的获取结果
    # 记录汇总日志
```

### 3. API 调用策略

#### 3.1 协议参数映射
```python
SCHEME_MAPPING = {
    'http': 'http',
    'socks4': 'socks4', 
    'socks5': 'socks5'
}
```

#### 3.2 API 调用参数
每个协议使用相同的 API 参数，只修改 `schemes` 字段：
- `id`: TOKEN_ID
- `size`: SIZE (50)
- `schemes`: 动态设置 ('http', 'socks4', 'socks5')
- `support_https`: 'true'
- `restime_within_ms`: 5000
- `format`: 'txt2_2'

## 数据模型

### 代理字符串格式

根据您提供的示例，UU Proxy API 返回的格式：
- HTTP: `http://host:port` 或 `host:port`
- SOCKS4: `socks4://host:port`
- SOCKS5: `socks5://host:port`

### Proxy 对象创建

使用现有的 `convert_proxy_or_proxies()` 函数处理：
```python
# 该函数已支持协议感知，能够正确解析：
proxy = convert_proxy_or_proxies("socks4://**************:10057")
proxy = convert_proxy_or_proxies("socks5://**************:10057")
```

## 错误处理

### 1. API 调用错误处理

```python
try:
    proxy_text = get_proxies(API_SERVER_ADDRESS, TOKEN_ID, SIZE, scheme, ...)
except Exception as e:
    logger.error(f'UU代理API调用失败 [协议: {scheme}]: {e}')
    continue  # 继续处理下一个协议
```

### 2. 代理解析错误处理

```python
try:
    proxy = convert_proxy_or_proxies(proxy_line)
except Exception as e:
    logger.error(f'解析代理失败 [协议: {scheme}] {proxy_line}: {e}')
    continue  # 继续处理下一个代理
```

### 3. 协议验证错误处理

```python
def _validate_scheme(self, scheme: str) -> bool:
    """验证协议是否支持"""
    if scheme not in SUPPORTED_SCHEMES:
        logger.warning(f'不支持的协议类型: {scheme}')
        return False
    return True
```

## 测试策略

### 1. 单元测试覆盖

- `_get_enabled_schemes()` 方法测试
- `_crawl_by_scheme()` 方法测试  
- 协议验证逻辑测试
- 错误处理逻辑测试

### 2. 集成测试

- 多协议爬取完整流程测试
- API 调用失败场景测试
- 代理解析异常场景测试

### 3. 性能测试

- 多协议并发爬取性能测试
- 内存使用情况监控
- API 调用频率控制测试

## 实现细节

### 1. 向后兼容性

保持现有接口不变，确保：
- `crawl()` 方法签名不变
- 现有配置仍然有效
- 日志格式保持一致

### 2. 配置灵活性

支持通过环境变量或配置文件控制：
```python
import os
ENABLED_SCHEMES = os.getenv('UU_PROXY_SCHEMES', 'http,socks4,socks5').split(',')
```

### 3. 日志增强

增加协议相关的日志信息：
```python
logger.info(f'开始爬取UU代理 [协议: {scheme}]')
logger.debug(f'获取到 {len(proxy_lines)} 个 {scheme} 代理')
logger.info(f'UU代理爬取完成 [总计: HTTP={http_count}, SOCKS4={socks4_count}, SOCKS5={socks5_count}]')
```

### 4. 资源管理

- 合理控制 API 调用频率，避免触发限制
- 实现适当的重试机制
- 优化内存使用，及时释放不需要的数据

## 部署考虑

### 1. 配置管理

- 支持通过环境变量配置启用的协议
- 提供配置验证和默认值处理
- 支持运行时配置更新

### 2. 监控和告警

- 添加协议级别的成功率监控
- 实现 API 调用失败告警
- 提供代理质量统计报告

### 3. 扩展性

- 设计支持未来新协议类型的添加
- 预留协议特定配置的扩展点
- 支持协议优先级和权重配置