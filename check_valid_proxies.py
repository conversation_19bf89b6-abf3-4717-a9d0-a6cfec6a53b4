import asyncio
from http_proxy_pool.storages.redis_client import AsyncRedisClient
from http_proxy_pool.setting import PROXY_SCORE_MAX

async def check_valid_proxies():
    redis = AsyncRedisClient()
    
    # 获取总代理数量
    total_count = await redis.count()
    print(f'总代理数量: {total_count}')
    
    # 获取满分代理数量 (100分)
    valid_count = await redis.zcount(min_score=PROXY_SCORE_MAX, max_score=PROXY_SCORE_MAX)
    print(f'满分代理数量 (100分): {valid_count}')
    
    # 获取各分数段的代理数量
    score_ranges = [
        (0, 10, "新代理"),
        (11, 50, "低分代理"), 
        (51, 99, "中分代理"),
        (100, 100, "满分代理")
    ]
    
    print("\n分数分布:")
    for min_score, max_score, desc in score_ranges:
        count = await redis.zcount(min_score=min_score, max_score=max_score)
        print(f'  {desc} ({min_score}-{max_score}分): {count}个')
    
    # 获取前10个代理看看分数
    print(f"\n前10个代理的分数:")
    cursor, proxies_with_scores = await redis._client.zscan("proxies:universal", 0, count=10)
    for proxy_str, score in proxies_with_scores:
        print(f'  {proxy_str}: {score}分')
    
    await redis.close()

if __name__ == '__main__':
    asyncio.run(check_valid_proxies())