[package]
name = "http-proxy-ipv6-pool"
version = "0.1.0"
edition = "2021"
authors = ["zu1k <<EMAIL>>"]
description = "Http proxy, every request from a separate IPv6 address."
readme = "README.md"
license = "MIT"
homepage = "https://github.com/zu1k/http-proxy-ipv6-pool"
repository = "https://github.com/zu1k/http-proxy-ipv6-pool"


[dependencies]
cidr = "0.2"
getopts = "0.2"
hyper = { version = "0.14", features = ["client", "server", "http1", "runtime"] }
tokio = { version = "1", features = ["net", "rt-multi-thread", "macros", "io-util"] }
rand = "0.8"
base64 = "0.21"


[profile.release]
strip = true
lto = true
opt-level = "s"
codegen-units = 1