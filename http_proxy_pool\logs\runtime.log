2025-07-27 14:10:15.577 | INFO     | http_proxy_pool.crawlers.private.uu_proxy:crawl:145 - 开始UU代理多协议爬取，启用协议: ['http', 'socks4', 'socks5']
2025-07-27 14:10:15.580 | INFO     | http_proxy_pool.crawlers.private.uu_proxy:_crawl_by_scheme:91 - 开始爬取UU代理 [协议: http]
2025-07-27 14:10:16.385 | INFO     | http_proxy_pool.crawlers.private.uu_proxy:_crawl_by_scheme:132 - 完成 http 协议爬取，成功获取 42 个代理
2025-07-27 14:10:16.386 | INFO     | http_proxy_pool.crawlers.private.uu_proxy:_crawl_by_scheme:91 - 开始爬取UU代理 [协议: socks4]
2025-07-27 14:34:43.681 | INFO     | __main__:run:37 - 启动代理获取器
2025-07-27 14:34:45.721 | INFO     | __main__:_crawl_all_proxies:22 - 启动 2 个爬虫获取代理
2025-07-27 14:34:45.724 | INFO     | http_proxy_pool.crawlers.base:crawl:40 - fetching https://gist.githubusercontent.com/yyh357/566fc1afc659e42df1186785775991f6/raw/082ae98d2a434ae2b6880209850080df73e9533b/gistfile1.txt
2025-07-27 14:34:46.654 | INFO     | http_proxy_pool.crawlers.private.uu_proxy:crawl:56 - HTTP: 47 个代理
2025-07-27 14:34:46.943 | INFO     | http_proxy_pool.crawlers.private.txt_proxies:process:208 - 从 https://gist.githubusercontent.com/yyh357/566fc1afc659e42df1186785775991f6/raw/082ae98d2a434ae2b6880209850080df73e9533b/gistfile1.txt 获取到 22 个 HTTP 代理，拒绝 0 个无效代理
2025-07-27 14:34:46.990 | INFO     | http_proxy_pool.crawlers.private.uu_proxy:crawl:56 - SOCKS4: 50 个代理
2025-07-27 14:34:47.303 | INFO     | http_proxy_pool.crawlers.private.uu_proxy:crawl:56 - SOCKS5: 50 个代理
2025-07-27 14:34:47.303 | INFO     | http_proxy_pool.crawlers.private.uu_proxy:crawl:61 - UU代理爬取完成，总计: 147 个代理
2025-07-27 14:34:47.304 | INFO     | __main__:_crawl_all_proxies:32 - 爬虫完成，共获取 169 个代理
2025-07-27 14:34:47.416 | INFO     | http_proxy_pool.storages.protocol_aware_redis_client:add_batch_by_protocol:93 - Added 4 proxies to proxies:http
2025-07-27 14:34:47.527 | INFO     | http_proxy_pool.storages.protocol_aware_redis_client:add_batch_by_protocol:93 - Added 0 proxies to proxies:socks4
2025-07-27 14:34:47.644 | INFO     | http_proxy_pool.storages.protocol_aware_redis_client:add_batch_by_protocol:93 - Added 15 proxies to proxies:socks5
2025-07-27 14:34:47.644 | INFO     | __main__:run:55 - 代理存储完成:
2025-07-27 14:34:47.644 | INFO     | __main__:run:58 -   proxies:http: 4 个
2025-07-27 14:34:47.645 | INFO     | __main__:run:58 -   proxies:socks5: 15 个
2025-07-27 14:34:47.645 | INFO     | __main__:run:59 - 总计: 19 个代理
2025-07-27 16:16:21.475 | WARNING  | http_proxy_pool.crawlers.base:_fetch_with_429_handling:42 - Received 429 rate limit for http://test.com, waiting 5s before retry 1/3
2025-07-27 16:16:21.477 | WARNING  | http_proxy_pool.crawlers.base:_fetch_with_429_handling:42 - Received 429 rate limit for http://test.com, waiting 10s before retry 2/3
2025-07-27 16:16:21.477 | WARNING  | http_proxy_pool.crawlers.base:_fetch_with_429_handling:42 - Received 429 rate limit for http://test.com, waiting 20s before retry 3/3
2025-07-27 16:16:21.478 | ERROR    | http_proxy_pool.crawlers.base:_fetch_with_429_handling:46 - Max 429 retries exceeded for http://test.com
2025-07-27 16:16:21.480 | WARNING  | http_proxy_pool.crawlers.base:_fetch_with_429_handling:42 - Received 429 rate limit for http://test.com, waiting 5s before retry 1/3
2025-07-27 16:16:21.480 | WARNING  | http_proxy_pool.crawlers.base:_fetch_with_429_handling:42 - Received 429 rate limit for http://test.com, waiting 10s before retry 2/3
2025-07-27 16:16:33.932 | WARNING  | http_proxy_pool.crawlers.base:_fetch_with_429_handling:42 - Received 429 rate limit for http://test.com, waiting 5s before retry 1/3
2025-07-27 16:16:33.934 | WARNING  | http_proxy_pool.crawlers.base:_fetch_with_429_handling:42 - Received 429 rate limit for http://test.com, waiting 10s before retry 2/3
2025-07-27 16:16:33.935 | WARNING  | http_proxy_pool.crawlers.base:_fetch_with_429_handling:42 - Received 429 rate limit for http://test.com, waiting 20s before retry 3/3
2025-07-27 16:16:33.935 | ERROR    | http_proxy_pool.crawlers.base:_fetch_with_429_handling:46 - Max 429 retries exceeded for http://test.com
2025-07-27 16:16:33.938 | WARNING  | http_proxy_pool.crawlers.base:_fetch_with_429_handling:42 - Received 429 rate limit for http://test.com, waiting 5s before retry 1/3
2025-07-27 16:16:33.938 | WARNING  | http_proxy_pool.crawlers.base:_fetch_with_429_handling:42 - Received 429 rate limit for http://test.com, waiting 10s before retry 2/3
2025-07-27 16:22:00.498 | WARNING  | http_proxy_pool.crawlers.base:_fetch_with_429_handling:66 - HTTP 521 for https://openproxy.space/list/http - Content length: 6989 - Headers: {'Date': 'Sun, 27 Jul 2025 08:22:00 GMT', 'Content-Type': 'text/html; charset=UTF-8', 'Content-Length': '6989', 'Connection': 'keep-alive', 'Cache-Control': 'private, max-age=0, no-store, no-cache, must-revalidate, post-check=0, pre-check=0', 'Expires': 'Thu, 01 Jan 1970 00:00:01 GMT', 'Referrer-Policy': 'same-origin', 'X-Frame-Options': 'SAMEORIGIN', 'Server': 'cloudflare', 'CF-RAY': '965ab9fb1c86262c-NRT'}
2025-07-27 16:22:01.070 | WARNING  | http_proxy_pool.crawlers.base:_fetch_with_429_handling:51 - URL returned empty content (200 OK but no data): https://raw.githubusercontent.com/proxifly/free-proxy-list/refs/heads/main/proxies/protocols/https/data.txt
2025-07-27 16:22:01.712 | WARNING  | http_proxy_pool.crawlers.base:_fetch_with_429_handling:66 - HTTP 404 for https://raw.githubusercontent.com/AlestackOverglow/proxy-list/refs/heads/main/proxies_with_protocol.txt - Content length: 14 - Headers: {'Connection': 'keep-alive', 'Content-Length': '14', 'Content-Security-Policy': "default-src 'none'; style-src 'unsafe-inline'; sandbox", 'Strict-Transport-Security': 'max-age=31536000', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'deny', 'X-XSS-Protection': '1; mode=block', 'Content-Type': 'text/plain; charset=utf-8', 'X-GitHub-Request-Id': 'A184:47E8A:91F34:1E6B40:6885E1A7', 'Accept-Ranges': 'bytes', 'Date': 'Sun, 27 Jul 2025 08:22:01 GMT', 'Via': '1.1 varnish', 'X-Served-By': 'cache-nrt-rjtt7900077-NRT', 'X-Cache': 'MISS', 'X-Cache-Hits': '0', 'X-Timer': 'S1753604521.498609,VS0,VE180', 'Vary': 'Authorization,Accept-Encoding', 'Access-Control-Allow-Origin': '*', 'Cross-Origin-Resource-Policy': 'cross-origin', 'X-Fastly-Request-ID': '4dcafde21ed04aa4c6889448714226061b1c30c1', 'Expires': 'Sun, 27 Jul 2025 08:27:01 GMT', 'Source-Age': '0'}
