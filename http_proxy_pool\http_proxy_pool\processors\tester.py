import asyncio
import aiohttp
from loguru import logger
from http_proxy_pool.schemas import Proxy
from http_proxy_pool.storages.redis_client import AsyncRedisClient
from http_proxy_pool.storages.protocol_aware_redis_client import ProtocolAwareRedisClient
from http_proxy_pool.testers.protocol_aware_tester import ProtocolAwareTester
from http_proxy_pool.setting import TEST_TIMEOUT, TEST_BATCH, TEST_URL, TEST_VALID_STATUS, TEST_ANONYMOUS, \
    TEST_DONT_SET_MAX_SCORE
from aiohttp import ClientProxyConnectionError, ServerDisconnectedError, ClientOSError, ClientHttpProxyError
from asyncio import TimeoutError
from http_proxy_pool.testers import __all__ as testers_cls

EXCEPTIONS = (
    ClientProxyConnectionError,
    ConnectionRefusedError,
    TimeoutError,
    ServerDisconnectedError,
    ClientOSError,
    ClientHttpProxyError,
    AssertionError
)


class Tester(object):
    """
    协议感知的代理测试器
    支持HTTP、SOCKS4、SOCKS5代理的专门测试
    """

    def __init__(self, use_protocol_aware=True):
        """
        初始化测试器
        
        :param use_protocol_aware: 是否使用协议感知测试
        """
        self.use_protocol_aware = use_protocol_aware
        
        if use_protocol_aware:
            self.redis = ProtocolAwareRedisClient()
            self.protocol_tester = ProtocolAwareTester()
        else:
            self.redis = AsyncRedisClient()
        
        self.testers_cls = testers_cls
        self.testers = [tester_cls() for tester_cls in self.testers_cls]

    async def _test_anonymity(self, session, proxy: Proxy):
        """测试代理匿名性"""
        url = 'https://httpbin.org/ip'
        async with session.get(url, timeout=TEST_TIMEOUT) as response:
            resp_json = await response.json()
            origin_ip = resp_json['origin']
            
        async with session.get(url, proxy=proxy.string(), timeout=TEST_TIMEOUT) as response:
            resp_json = await response.json()
            anonymous_ip = resp_json['origin']
            logger.debug(f'anonymous ip is {anonymous_ip}')
            
        assert origin_ip != anonymous_ip
        assert proxy.host == anonymous_ip

    async def _handle_test_result(self, proxy: Proxy, is_valid: bool, key=None, tester=None):
        """处理测试结果（协议感知版本）"""
        if is_valid:
            if key and tester:
                if tester.test_dont_set_max_score:
                    logger.info(f'key[{key}] proxy {proxy.string()} is valid, remain current score')
                else:
                    if self.use_protocol_aware:
                        await self.redis.max_by_protocol(proxy, tester.proxy_score_max)
                    else:
                        await self.redis.max(proxy, key, tester.proxy_score_max)
                    logger.info(f'key[{key}] proxy {proxy.string()} is valid, set max score')
            else:
                if TEST_DONT_SET_MAX_SCORE:
                    logger.debug(f'proxy {proxy.string()} is valid, remain current score')
                else:
                    if self.use_protocol_aware:
                        await self.redis.max_by_protocol(proxy)
                    else:
                        await self.redis.max(proxy)
                    logger.debug(f'proxy {proxy.string()} is valid, set max score')
        else:
            if key and tester:
                if self.use_protocol_aware:
                    await self.redis.decrease_by_protocol(proxy, tester.proxy_score_min)
                else:
                    await self.redis.decrease(proxy, tester.key, tester.proxy_score_min)
                logger.info(f'key[{key}] proxy {proxy.string()} is invalid, decrease score')
            else:
                if self.use_protocol_aware:
                    await self.redis.decrease_by_protocol(proxy)
                else:
                    await self.redis.decrease(proxy)
                logger.debug(f'proxy {proxy.string()} is invalid, decrease score')

    async def test(self, proxy: Proxy):
        """
        测试单个代理（协议感知版本）
        :param proxy: Proxy object
        :return:
        """
        try:
            logger.debug(f'开始测试代理: {proxy.string()} (协议: {proxy.protocol})')
            
            # 使用协议感知测试
            if self.use_protocol_aware:
                is_valid = await self.protocol_tester.test_proxy_by_protocol(proxy)
                await self._handle_test_result(proxy, is_valid)
                
                # 如果基础测试通过，进行扩展测试
                if is_valid:
                    await self._run_extended_tests(proxy)
            else:
                # 传统测试方法（向后兼容）
                await self._run_traditional_test(proxy)
                
        except EXCEPTIONS as e:
            # 处理测试失败
            await self._handle_test_failure(proxy, e)
    
    async def _run_traditional_test(self, proxy: Proxy):
        """运行传统测试方法（向后兼容）"""
        async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=False)) as session:
            # 匿名性测试
            if TEST_ANONYMOUS:
                logger.debug(f'进行匿名性测试: {proxy.string()}')
                await self._test_anonymity(session, proxy)
                logger.debug(f'匿名性测试通过: {proxy.string()}')
            
            # 基础连通性测试
            logger.debug(f'进行连通性测试: {proxy.string()} -> {TEST_URL}')
            async with session.get(TEST_URL, proxy=f'http://{proxy.string()}', timeout=TEST_TIMEOUT,
                                   allow_redirects=False) as response:
                is_valid = response.status in TEST_VALID_STATUS
                logger.debug(f'连通性测试结果: {proxy.string()} -> 状态码 {response.status}, 有效: {is_valid}')
                await self._handle_test_result(proxy, is_valid)
            
            # 扩展测试器测试
            await self._run_extended_tests(proxy, session)
    
    async def _run_extended_tests(self, proxy: Proxy, session=None):
        """运行扩展测试器测试"""
        for tester in self.testers:
            key = tester.key
            
            # 检查代理是否存在于扩展测试器的键中
            exists_check = (await self.redis.exists_in_protocol(proxy) if self.use_protocol_aware 
                          else await self.redis.exists(proxy, key))
            
            if exists_check:
                logger.debug(f'进行扩展测试: {proxy.string()} -> {key}')
                
                if session is None:
                    # 创建新的session用于协议感知测试
                    async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=False)) as new_session:
                        await self._run_single_extended_test(new_session, proxy, tester, key)
                else:
                    # 使用现有session（传统测试）
                    await self._run_single_extended_test(session, proxy, tester, key)
    
    async def _run_single_extended_test(self, session, proxy: Proxy, tester, key: str):
        """运行单个扩展测试"""
        try:
            test_url = tester.test_url
            headers = tester.headers()
            cookies = tester.cookies()
            
            async with session.get(test_url, proxy=f'http://{proxy.string()}',
                                   timeout=TEST_TIMEOUT,
                                   headers=headers,
                                   cookies=cookies,
                                   allow_redirects=False) as response:
                resp_text = await response.text()
                is_valid = await tester.parse(resp_text, test_url, proxy.string())
                logger.debug(f'扩展测试结果: {proxy.string()} -> {key}, 有效: {is_valid}')
                await self._handle_test_result(proxy, is_valid, key, tester)
        except Exception as e:
            logger.debug(f'扩展测试失败: {proxy.string()} -> {key}, 错误: {type(e).__name__}: {str(e)[:100]}')
            await self._handle_test_result(proxy, False, key, tester)
    
    async def _handle_test_failure(self, proxy: Proxy, exception):
        """处理测试失败"""
        if self.use_protocol_aware:
            # 协议感知的失败处理
            decrease_tasks = [self.redis.decrease_by_protocol(proxy)]
            decrease_tasks.extend([
                self.redis.decrease_by_protocol(proxy, tester.proxy_score_min)
                for tester in self.testers
            ])
        else:
            # 传统的失败处理
            decrease_tasks = [self.redis.decrease(proxy)]
            decrease_tasks.extend([
                self.redis.decrease(proxy, tester.key, tester.proxy_score_min)
                for tester in self.testers
            ])
        
        await asyncio.gather(*decrease_tasks, return_exceptions=True)
        logger.debug(f'代理测试失败: {proxy.string()} -> {type(exception).__name__}: {str(exception)[:100]}')

    @logger.catch
    async def run(self):
        """
        协议感知的测试主方法
        :return:
        """
        logger.info('启动协议感知测试器...')
        
        if self.use_protocol_aware:
            # 获取各协议的代理统计
            protocol_counts = await self.redis.count_by_protocol()
            total_count = sum(protocol_counts.values())
            
            logger.info(f'协议分布统计:')
            for protocol, count in protocol_counts.items():
                logger.info(f'  {protocol.upper()}: {count} 个代理')
            logger.info(f'总计: {total_count} 个代理需要测试')
            
            if total_count == 0:
                logger.info('没有代理需要测试')
                return
            
            # 按协议分别测试
            await self._run_protocol_aware_testing(protocol_counts)
        else:
            # 传统测试方法
            await self._run_traditional_testing()
        
        # 显示最终统计
        if self.use_protocol_aware:
            await self._show_final_protocol_stats()
            
            # 显示协议测试器统计
            tester_stats = self.protocol_tester.get_stats()
            logger.info('协议测试器统计:')
            for key, value in tester_stats.items():
                if 'rate' in key:
                    logger.info(f'  {key}: {value}%')
                elif value > 0:
                    logger.info(f'  {key}: {value}')
        
        await self.redis.close()
        logger.info('协议感知测试器完成')
    
    async def _run_protocol_aware_testing(self, protocol_counts):
        """运行协议感知测试"""
        from http_proxy_pool.utils.protocol_detector import ProtocolType
        
        total_tested = 0
        
        for protocol_type in ProtocolType:
            protocol_name = protocol_type.value
            count = protocol_counts.get(protocol_name, 0)
            
            if count == 0:
                continue
            
            logger.info(f'开始测试 {protocol_name.upper()} 代理 ({count} 个)')
            
            # 获取该协议的所有代理
            try:
                proxies = await self.redis.get_all_by_protocol(protocol_type)
                if proxies:
                    # 分批测试
                    batch_tested = await self._test_proxies_in_batches(proxies, protocol_name.upper())
                    total_tested += batch_tested
                    logger.info(f'{protocol_name.upper()} 代理测试完成，测试了 {batch_tested} 个代理')
            except Exception as e:
                logger.error(f'测试 {protocol_name.upper()} 代理时出错: {e}')
        
        logger.info(f'协议感知测试完成，总共测试了 {total_tested} 个代理')
    
    async def _run_traditional_testing(self):
        """运行传统测试方法"""
        logger.info('使用传统测试方法...')
        count = await self.redis.count()
        logger.info(f'总共有 {count} 个代理需要测试')
        
        if count == 0:
            logger.info('没有代理需要测试')
            return
        
        total_tested = 0
        cursor = 0
        batch_count = 0
        
        while True:
            batch_count += 1
            logger.info(f'开始第 {batch_count} 批测试，游标: {cursor}，批次大小: {TEST_BATCH}')
            cursor, proxies = await self.redis.batch(cursor, count=TEST_BATCH)
            
            if proxies:
                batch_size = len(proxies)
                total_tested += batch_size
                logger.info(f'正在测试 {batch_size} 个代理... (总进度: {total_tested}/{count})')
                
                # 并发测试所有代理
                tasks = [self.test(proxy) for proxy in proxies]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 统计结果
                success_count = sum(1 for r in results if not isinstance(r, Exception))
                error_count = batch_size - success_count
                
                logger.info(f'第 {batch_count} 批完成: 成功 {success_count}, 异常 {error_count}')
                
            if not cursor:
                break
        
        final_count = await self.redis.count()
        logger.info(f'传统测试完成! 测试了 {total_tested} 个代理，当前剩余 {final_count} 个代理')
    
    async def _test_proxies_in_batches(self, proxies, protocol_name):
        """分批测试代理"""
        total_tested = 0
        batch_count = 0
        
        for i in range(0, len(proxies), TEST_BATCH):
            batch_count += 1
            batch = proxies[i:i + TEST_BATCH]
            batch_size = len(batch)
            total_tested += batch_size
            
            logger.info(f'测试 {protocol_name} 第 {batch_count} 批: {batch_size} 个代理')
            
            # 并发测试批次中的所有代理
            tasks = [self.test(proxy) for proxy in batch]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            error_count = batch_size - success_count
            
            logger.info(f'{protocol_name} 第 {batch_count} 批完成: 成功 {success_count}, 异常 {error_count}')
        
        return total_tested
    
    async def _show_final_protocol_stats(self):
        """显示最终的协议统计信息"""
        try:
            final_counts = await self.redis.count_by_protocol()
            final_stats = await self.redis.get_protocol_stats()
            
            logger.info('最终协议统计:')
            for protocol, count in final_counts.items():
                stats = final_stats.get(protocol, {})
                health_ratio = stats.get('health_ratio', 0) * 100
                logger.info(f'  {protocol.upper()}: {count} 个代理 (健康度: {health_ratio:.1f}%)')
        except Exception as e:
            logger.error(f'获取最终统计信息失败: {e}')



async def main(use_protocol_aware=True):
    """
    异步主函数
    
    :param use_protocol_aware: 是否使用协议感知测试
    """
    tester = Tester(use_protocol_aware=use_protocol_aware)
    await tester.run()

if __name__ == '__main__':
    import sys
    
    # 支持命令行参数控制是否使用协议感知测试
    use_protocol_aware = True
    if len(sys.argv) > 1 and sys.argv[1] == '--traditional':
        use_protocol_aware = False
        logger.info('使用传统测试模式')
    else:
        logger.info('使用协议感知测试模式')
    
    asyncio.run(main(use_protocol_aware))
