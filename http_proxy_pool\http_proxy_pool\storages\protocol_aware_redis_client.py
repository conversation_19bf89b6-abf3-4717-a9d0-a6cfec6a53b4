"""
协议感知的Redis客户端
继承AsyncRedisClient，添加协议分表存储功能
专为高性能代理池优化
"""

import asyncio
from typing import List, Dict, Optional, Tuple
from random import choice
from loguru import logger

from http_proxy_pool.storages.redis_client import AsyncRedisClient
from http_proxy_pool.schemas.proxy import Proxy
from http_proxy_pool.utils.protocol_detector import ProtocolDetector, ProtocolType
from http_proxy_pool.exceptions import PoolEmptyException, ProtocolDetectionException
from http_proxy_pool.setting import PROXY_SCORE_INIT, PROXY_SCORE_MAX, PROXY_SCORE_MIN


class ProtocolAwareRedisClient(AsyncRedisClient):
    """
    协议感知的Redis客户端
    支持按协议分表存储和获取代理
    """
    
    def __init__(self, base_key: str = "proxies", **kwargs):
        """
        初始化协议感知Redis客户端
        
        :param base_key: 基础Redis键名
        :param kwargs: 传递给父类的参数
        """
        super().__init__(**kwargs)
        self.base_key = base_key
        self._protocol_keys = ProtocolDetector.get_all_protocol_keys(base_key)
    
    async def add_by_protocol(self, proxy: Proxy, score: int = PROXY_SCORE_INIT) -> int:
        """
        根据协议添加代理到对应表
        
        :param proxy: 代理对象
        :param score: 初始分数
        :return: 添加结果 (1=成功, 0=已存在或失败)
        """
        if not proxy.validate():
            logger.warning(f'Invalid proxy {proxy}, rejected')
            return 0
        
        # 获取协议对应的Redis键
        redis_key = proxy.get_redis_key(self.base_key)
        
        # 使用标准化字符串作为存储键
        proxy_string = proxy.get_normalized_string()
        
        logger.debug(f'Adding proxy {proxy_string} to {redis_key} with score {score}')
        
        return await self.add(proxy, score, redis_key)
    
    async def add_batch_by_protocol(self, proxies: List[Proxy], score: int = PROXY_SCORE_INIT) -> Dict[str, int]:
        """
        批量添加代理到对应协议表（高性能版本）
        
        :param proxies: 代理列表
        :param score: 初始分数
        :return: 各协议表的添加结果统计
        """
        if not proxies:
            return {}
        
        # 按协议分组代理
        protocol_groups: Dict[str, List[Proxy]] = {}
        for proxy in proxies:
            if proxy.validate():
                redis_key = proxy.get_redis_key(self.base_key)
                if redis_key not in protocol_groups:
                    protocol_groups[redis_key] = []
                protocol_groups[redis_key].append(proxy)
            else:
                logger.warning(f'Invalid proxy {proxy}, rejected')
        
        # 并发批量添加到各协议表
        results = {}
        if protocol_groups:
            tasks = []
            for redis_key, proxy_group in protocol_groups.items():
                task = self.add_batch(proxy_group, score, redis_key)
                tasks.append((redis_key, task))
            
            # 等待所有任务完成
            for redis_key, task in tasks:
                try:
                    result = await task
                    results[redis_key] = result
                    logger.debug(f'Added {result} proxies to {redis_key}')
                except Exception as e:
                    logger.error(f'Failed to add proxies to {redis_key}: {e}')
                    results[redis_key] = 0
        
        return results
    
    async def get_by_protocol(self, protocol: ProtocolType, 
                            proxy_score_min: int = PROXY_SCORE_MIN,
                            proxy_score_max: int = PROXY_SCORE_MAX) -> Proxy:
        """
        从指定协议表获取代理
        
        :param protocol: 协议类型
        :param proxy_score_min: 最小分数
        :param proxy_score_max: 最大分数
        :return: 代理对象
        :raises PoolEmptyException: 协议池为空时抛出
        """
        redis_key = ProtocolDetector.get_redis_key_for_protocol(protocol, self.base_key)
        
        try:
            proxy = await self.random(redis_key, proxy_score_min, proxy_score_max)
            # 确保代理对象具有正确的协议信息
            if proxy and proxy.protocol != protocol.value:
                # 修正协议信息
                proxy.protocol = protocol.value
                logger.debug(f'修正代理协议信息: {proxy.string()} -> {protocol.value}')
            return proxy
        except PoolEmptyException:
            logger.warning(f'Protocol pool {protocol.value} is empty')
            raise PoolEmptyException(f'No available {protocol.value} proxies')
    
    async def get_random_any_protocol(self, 
                                    preferred_protocols: Optional[List[ProtocolType]] = None,
                                    proxy_score_min: int = PROXY_SCORE_MIN,
                                    proxy_score_max: int = PROXY_SCORE_MAX) -> Proxy:
        """
        从所有协议表随机获取代理（智能负载均衡）
        
        :param preferred_protocols: 优先协议列表，None表示所有协议
        :param proxy_score_min: 最小分数
        :param proxy_score_max: 最大分数
        :return: 代理对象
        :raises PoolEmptyException: 所有协议池都为空时抛出
        """
        protocols_to_try = preferred_protocols or list(ProtocolType)
        
        # 随机打乱协议顺序，实现负载均衡
        protocols_to_try = protocols_to_try.copy()
        from random import shuffle
        shuffle(protocols_to_try)
        
        # 尝试从各协议池获取代理
        for protocol in protocols_to_try:
            try:
                return await self.get_by_protocol(protocol, proxy_score_min, proxy_score_max)
            except PoolEmptyException:
                continue
        
        # 所有协议池都为空
        raise PoolEmptyException('All protocol pools are empty')
    
    async def count_by_protocol(self, protocol: Optional[ProtocolType] = None) -> Dict[str, int]:
        """
        获取各协议表的代理数量统计
        
        :param protocol: 指定协议，None表示统计所有协议
        :return: 协议到数量的映射
        """
        if protocol:
            redis_key = ProtocolDetector.get_redis_key_for_protocol(protocol, self.base_key)
            count = await self.count(redis_key)
            return {protocol.value: count}
        
        # 并发获取所有协议的数量
        tasks = []
        for protocol_type in ProtocolType:
            redis_key = ProtocolDetector.get_redis_key_for_protocol(protocol_type, self.base_key)
            task = self.count(redis_key)
            tasks.append((protocol_type.value, task))
        
        results = {}
        for protocol_name, task in tasks:
            try:
                count = await task
                results[protocol_name] = count
            except Exception as e:
                logger.error(f'Failed to count {protocol_name} proxies: {e}')
                results[protocol_name] = 0
        
        return results
    
    async def get_protocol_stats(self) -> Dict[str, Dict[str, int]]:
        """
        获取详细的协议统计信息
        
        :return: 包含各协议详细统计的字典
        """
        stats = {}
        
        # 并发获取各协议的详细统计
        tasks = []
        for protocol_type in ProtocolType:
            redis_key = ProtocolDetector.get_redis_key_for_protocol(protocol_type, self.base_key)
            
            # 为每个协议创建统计任务
            count_task = self.count(redis_key)
            high_score_task = self.zcount(redis_key, PROXY_SCORE_MAX, PROXY_SCORE_MAX)
            medium_score_task = self.zcount(redis_key, PROXY_SCORE_MAX // 2, PROXY_SCORE_MAX - 1)
            low_score_task = self.zcount(redis_key, PROXY_SCORE_MIN, PROXY_SCORE_MAX // 2 - 1)
            
            tasks.append((
                protocol_type.value,
                count_task,
                high_score_task,
                medium_score_task,
                low_score_task
            ))
        
        # 等待所有统计任务完成
        for protocol_name, count_task, high_task, medium_task, low_task in tasks:
            try:
                total_count = await count_task
                high_score_count = await high_task
                medium_score_count = await medium_task
                low_score_count = await low_task
                
                stats[protocol_name] = {
                    'total': total_count,
                    'high_score': high_score_count,
                    'medium_score': medium_score_count,
                    'low_score': low_score_count,
                    'health_ratio': high_score_count / total_count if total_count > 0 else 0.0
                }
            except Exception as e:
                logger.error(f'Failed to get stats for {protocol_name}: {e}')
                stats[protocol_name] = {
                    'total': 0,
                    'high_score': 0,
                    'medium_score': 0,
                    'low_score': 0,
                    'health_ratio': 0.0
                }
        
        return stats
    
    async def decrease_by_protocol(self, proxy: Proxy, 
                                 proxy_score_min: int = PROXY_SCORE_MIN) -> int:
        """
        减少指定协议代理的分数
        
        :param proxy: 代理对象
        :param proxy_score_min: 最小分数
        :return: 新分数
        """
        redis_key = proxy.get_redis_key(self.base_key)
        return await self.decrease(proxy, redis_key, proxy_score_min)
    
    async def max_by_protocol(self, proxy: Proxy, 
                            proxy_score_max: int = PROXY_SCORE_MAX) -> int:
        """
        设置指定协议代理为最高分
        
        :param proxy: 代理对象
        :param proxy_score_max: 最高分数
        :return: 操作结果
        """
        redis_key = proxy.get_redis_key(self.base_key)
        return await self.max(proxy, redis_key, proxy_score_max)
    
    async def exists_in_protocol(self, proxy: Proxy) -> bool:
        """
        检查代理是否存在于对应协议表中
        
        :param proxy: 代理对象
        :return: 是否存在
        """
        redis_key = proxy.get_redis_key(self.base_key)
        return await self.exists(proxy, redis_key)
    
    async def get_all_by_protocol(self, protocol: ProtocolType,
                                proxy_score_min: int = PROXY_SCORE_MIN,
                                proxy_score_max: int = PROXY_SCORE_MAX) -> List[Proxy]:
        """
        获取指定协议的所有代理
        
        :param protocol: 协议类型
        :param proxy_score_min: 最小分数
        :param proxy_score_max: 最大分数
        :return: 代理列表
        """
        redis_key = ProtocolDetector.get_redis_key_for_protocol(protocol, self.base_key)
        return await self.all(redis_key, proxy_score_min, proxy_score_max)
    
    async def cleanup_empty_protocols(self) -> Dict[str, int]:
        """
        清理空的协议表（维护操作）
        
        :return: 清理结果统计
        """
        cleanup_results = {}
        
        for protocol_type in ProtocolType:
            redis_key = ProtocolDetector.get_redis_key_for_protocol(protocol_type, self.base_key)
            try:
                # 删除分数为0的代理（已被标记为无效）
                removed_count = await self.zremrangebyscore(redis_key, 0, 0)
                cleanup_results[protocol_type.value] = removed_count
                
                if removed_count > 0:
                    logger.info(f'Cleaned up {removed_count} invalid proxies from {redis_key}')
            except Exception as e:
                logger.error(f'Failed to cleanup {redis_key}: {e}')
                cleanup_results[protocol_type.value] = 0
        
        return cleanup_results
    
    async def migrate_from_universal_table(self, universal_key: str = "proxies") -> Dict[str, int]:
        """
        从通用表迁移数据到协议分表
        
        :param universal_key: 通用表的Redis键
        :return: 迁移结果统计
        """
        logger.info(f'Starting migration from universal table: {universal_key}')
        
        migration_results = {protocol.value: 0 for protocol in ProtocolType}
        migration_results['failed'] = 0
        migration_results['total'] = 0
        
        try:
            # 获取通用表中的所有代理
            universal_proxies = await self.all(universal_key)
            migration_results['total'] = len(universal_proxies)
            
            if not universal_proxies:
                logger.info('No proxies found in universal table')
                return migration_results
            
            logger.info(f'Found {len(universal_proxies)} proxies to migrate')
            
            # 按协议分组并迁移
            for proxy in universal_proxies:
                try:
                    # 尝试检测协议
                    protocol_enum = proxy.get_protocol_enum()
                    if protocol_enum:
                        # 添加到协议表
                        result = await self.add_by_protocol(proxy, PROXY_SCORE_INIT)
                        if result > 0:
                            migration_results[protocol_enum.value] += 1
                        else:
                            migration_results['failed'] += 1
                    else:
                        # 无法检测协议，默认添加到HTTP表
                        proxy.protocol = 'http'
                        result = await self.add_by_protocol(proxy, PROXY_SCORE_INIT)
                        if result > 0:
                            migration_results['http'] += 1
                        else:
                            migration_results['failed'] += 1
                            
                except Exception as e:
                    logger.error(f'Failed to migrate proxy {proxy}: {e}')
                    migration_results['failed'] += 1
            
            # 记录迁移结果
            for protocol, count in migration_results.items():
                if protocol != 'total' and count > 0:
                    logger.info(f'Migrated {count} proxies to {protocol} table')
            
            if migration_results['failed'] > 0:
                logger.warning(f'Failed to migrate {migration_results["failed"]} proxies')
            
        except Exception as e:
            logger.error(f'Migration failed: {e}')
            migration_results['failed'] = migration_results['total']
        
        return migration_results
