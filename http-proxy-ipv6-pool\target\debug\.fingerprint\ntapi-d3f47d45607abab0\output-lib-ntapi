{"$message_type":"diagnostic","message":"reference to packed field is unaligned","code":{"code":"E0793","explanation":"An unaligned reference to a field of a [packed] struct got created.\n\nErroneous code example:\n\n```compile_fail,E0793\n#[repr(packed)]\npub struct Foo {\n    field1: u64,\n    field2: u8,\n}\n\nunsafe {\n    let foo = Foo { field1: 0, field2: 0 };\n    // Accessing the field directly is fine.\n    let val = foo.field1;\n    // A reference to a packed field causes a error.\n    let val = &foo.field1; // ERROR\n    // An implicit `&` is added in format strings, causing the same error.\n    println!(\"{}\", foo.field1); // ERROR\n}\n```\n\nCreating a reference to an insufficiently aligned packed field is\n[undefined behavior] and therefore disallowed. Using an `unsafe` block does not\nchange anything about this. Instead, the code should do a copy of the data in\nthe packed field or use raw pointers and unaligned accesses.\n\n```\n#[repr(packed)]\npub struct Foo {\n    field1: u64,\n    field2: u8,\n}\n\nunsafe {\n    let foo = Foo { field1: 0, field2: 0 };\n\n    // Instead of a reference, we can create a raw pointer...\n    let ptr = std::ptr::addr_of!(foo.field1);\n    // ... and then (crucially!) access it in an explicitly unaligned way.\n    let val = unsafe { ptr.read_unaligned() };\n    // This would *NOT* be correct:\n    // let val = unsafe { *ptr }; // Undefined Behavior due to unaligned load!\n\n    // For formatting, we can create a copy to avoid the direct reference.\n    let copy = foo.field1;\n    println!(\"{}\", copy);\n    // Creating a copy can be written in a single line with curly braces.\n    // (This is equivalent to the two lines above.)\n    println!(\"{}\", { foo.field1 });\n}\n```\n\n### Additional information\n\nNote that this error is specifically about *references* to packed fields.\nDirect by-value access of those fields is fine, since then the compiler has\nenough information to generate the correct kind of access.\n\nSee [issue #82523] for more information.\n\n[packed]: https://doc.rust-lang.org/reference/type-layout.html#the-alignment-modifiers\n[undefined behavior]: https://doc.rust-lang.org/reference/behavior-considered-undefined.html\n[issue #82523]: https://github.com/rust-lang/rust/issues/82523\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ntapi-0.3.7\\src\\ntexapi.rs","byte_start":96791,"byte_end":96827,"line_start":2783,"line_end":2783,"column_start":52,"column_end":88,"is_primary":true,"text":[{"text":"        *tick_count.QuadPart_mut() = read_volatile(&(*USER_SHARED_DATA).u.TickCountQuad);","highlight_start":52,"highlight_end":88}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"packed structs are only aligned by one byte, and many modern architectures penalize unaligned field accesses","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"creating a misaligned reference is undefined behavior (even if that reference is never dereferenced)","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"copy the field contents to a local variable, or replace the reference with a raw pointer and use `read_unaligned`/`write_unaligned` (loads and stores via `*p` must be properly aligned even when using raw pointers)","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0793]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: reference to packed field is unaligned\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ntapi-0.3.7\\src\\ntexapi.rs:2783:52\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2783\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        *tick_count.QuadPart_mut() = read_volatile(&(*USER_SHARED_DATA).u.TickCountQuad);\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: packed structs are only aligned by one byte, and many modern architectures penalize unaligned field accesses\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: creating a misaligned reference is undefined behavior (even if that reference is never dereferenced)\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: copy the field contents to a local variable, or replace the reference with a raw pointer and use `read_unaligned`/`write_unaligned` (loads and stores via `*p` must be properly aligned even when using raw pointers)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"reference to packed field is unaligned","code":{"code":"E0793","explanation":"An unaligned reference to a field of a [packed] struct got created.\n\nErroneous code example:\n\n```compile_fail,E0793\n#[repr(packed)]\npub struct Foo {\n    field1: u64,\n    field2: u8,\n}\n\nunsafe {\n    let foo = Foo { field1: 0, field2: 0 };\n    // Accessing the field directly is fine.\n    let val = foo.field1;\n    // A reference to a packed field causes a error.\n    let val = &foo.field1; // ERROR\n    // An implicit `&` is added in format strings, causing the same error.\n    println!(\"{}\", foo.field1); // ERROR\n}\n```\n\nCreating a reference to an insufficiently aligned packed field is\n[undefined behavior] and therefore disallowed. Using an `unsafe` block does not\nchange anything about this. Instead, the code should do a copy of the data in\nthe packed field or use raw pointers and unaligned accesses.\n\n```\n#[repr(packed)]\npub struct Foo {\n    field1: u64,\n    field2: u8,\n}\n\nunsafe {\n    let foo = Foo { field1: 0, field2: 0 };\n\n    // Instead of a reference, we can create a raw pointer...\n    let ptr = std::ptr::addr_of!(foo.field1);\n    // ... and then (crucially!) access it in an explicitly unaligned way.\n    let val = unsafe { ptr.read_unaligned() };\n    // This would *NOT* be correct:\n    // let val = unsafe { *ptr }; // Undefined Behavior due to unaligned load!\n\n    // For formatting, we can create a copy to avoid the direct reference.\n    let copy = foo.field1;\n    println!(\"{}\", copy);\n    // Creating a copy can be written in a single line with curly braces.\n    // (This is equivalent to the two lines above.)\n    println!(\"{}\", { foo.field1 });\n}\n```\n\n### Additional information\n\nNote that this error is specifically about *references* to packed fields.\nDirect by-value access of those fields is fine, since then the compiler has\nenough information to generate the correct kind of access.\n\nSee [issue #82523] for more information.\n\n[packed]: https://doc.rust-lang.org/reference/type-layout.html#the-alignment-modifiers\n[undefined behavior]: https://doc.rust-lang.org/reference/behavior-considered-undefined.html\n[issue #82523]: https://github.com/rust-lang/rust/issues/82523\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ntapi-0.3.7\\src\\ntexapi.rs","byte_start":97690,"byte_end":97726,"line_start":2807,"line_end":2807,"column_start":25,"column_end":61,"is_primary":true,"text":[{"text":"        ((read_volatile(&(*USER_SHARED_DATA).u.TickCountQuad)","highlight_start":25,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"packed structs are only aligned by one byte, and many modern architectures penalize unaligned field accesses","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"creating a misaligned reference is undefined behavior (even if that reference is never dereferenced)","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"copy the field contents to a local variable, or replace the reference with a raw pointer and use `read_unaligned`/`write_unaligned` (loads and stores via `*p` must be properly aligned even when using raw pointers)","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0793]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: reference to packed field is unaligned\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\ntapi-0.3.7\\src\\ntexapi.rs:2807:25\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2807\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ((read_volatile(&(*USER_SHARED_DATA).u.TickCountQuad)\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: packed structs are only aligned by one byte, and many modern architectures penalize unaligned field accesses\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: creating a misaligned reference is undefined behavior (even if that reference is never dereferenced)\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: copy the field contents to a local variable, or replace the reference with a raw pointer and use `read_unaligned`/`write_unaligned` (loads and stores via `*p` must be properly aligned even when using raw pointers)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 2 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0793`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0793`.\u001b[0m\n"}
