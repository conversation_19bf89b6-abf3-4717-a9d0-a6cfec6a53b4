version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: proxy-pool-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  proxy-pool-api:
    build: .
    container_name: proxy-pool-api
    restart: unless-stopped
    ports:
      - "5555:5555"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - API_HOST=0.0.0.0
      - API_PORT=5555
    depends_on:
      redis:
        condition: service_healthy
    command: python -m http_proxy_pool.processors.protocol_aware_server
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5555/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  proxy-pool-getter:
    build: .
    container_name: proxy-pool-getter
    restart: unless-stopped
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      redis:
        condition: service_healthy
      proxy-pool-api:
        condition: service_healthy
    command: python -c "
import asyncio
from http_proxy_pool.processors.getter import Getter

async def run_getter():
    while True:
        try:
            getter = Getter()
            await getter.run()
            await getter.redis.close()
            await asyncio.sleep(300)
        except Exception as e:
            print(f'Getter error: {e}')
            await asyncio.sleep(60)

asyncio.run(run_getter())
"

  proxy-pool-tester:
    build: .
    container_name: proxy-pool-tester
    restart: unless-stopped
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      redis:
        condition: service_healthy
      proxy-pool-api:
        condition: service_healthy
    command: python -c "
import asyncio
from http_proxy_pool.processors.tester import Tester

async def run_tester():
    while True:
        try:
            tester = Tester()
            await tester.run()
            await asyncio.sleep(600)
        except Exception as e:
            print(f'Tester error: {e}')
            await asyncio.sleep(120)

asyncio.run(run_tester())
"

volumes:
  redis_data:
    driver: local

networks:
  default:
    name: proxy-pool-network
