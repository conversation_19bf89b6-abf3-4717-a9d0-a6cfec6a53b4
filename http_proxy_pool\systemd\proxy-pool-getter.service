[Unit]
Description=Protocol Aware Proxy Pool Getter
After=network.target redis.service proxy-pool-api.service
Requires=redis.service

[Service]
Type=simple
User=proxy-pool
Group=proxy-pool
WorkingDirectory=D:\onedrive\proxy_pool\http_proxy_pool
Environment=PYTHONPATH=D:\onedrive\proxy_pool\http_proxy_pool

# Getter服务
ExecStart=/usr/bin/python3 -c "
import asyncio
from http_proxy_pool.processors.getter import Getter

async def run_getter():
    while True:
        try:
            getter = Getter()
            await getter.run()
            await getter.redis.close()
            await asyncio.sleep(300)  # 5分钟间隔
        except Exception as e:
            print(f'Getter error: {e}')
            await asyncio.sleep(60)   # 错误后1分钟重试

asyncio.run(run_getter())
"

Restart=always
RestartSec=30

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=proxy-pool-getter

[Install]
WantedBy=multi-user.target
