import pkgutil
import importlib
import inspect
from .base import BaseTester


def _discover_testers():
    """动态发现所有BaseTester子类"""
    classes = []
    for loader, name, is_pkg in pkgutil.walk_packages(__path__):
        try:
            module_name = f'{__name__}.{name}'
            module = importlib.import_module(module_name)
            for attr_name, value in inspect.getmembers(module):
                if (inspect.isclass(value) and 
                    issubclass(value, BaseTester) and 
                    value is not BaseTester and 
                    not getattr(value, 'ignore', False)):
                    classes.append(value)
        except Exception:
            # 忽略导入错误，继续处理其他模块
            continue
    return classes


__all__ = _discover_testers()

