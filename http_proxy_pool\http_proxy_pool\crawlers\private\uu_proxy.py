# encoding: utf-8
import urllib.request
import urllib.parse
from loguru import logger
from http_proxy_pool.utils.proxy import convert_proxy_or_proxies
from http_proxy_pool.crawlers.base import BaseCrawler

# UU Proxy API配置
API_URL = 'http://uu-proxy.com/api/get_proxies'
TOKEN_ID = 'ZD48ZCL4JC'
SCHEMES = ['http', 'socks4', 'socks5']


class UUProxyCrawler(BaseCrawler):
    """UU代理爬虫 - 支持HTTP、SOCKS4、SOCKS5"""

    def _get_proxies(self, scheme):
        """获取指定协议的代理"""
        params = {
            'id': TOKEN_ID,
            'size': 50,
            'schemes': scheme,
            'support_https': 'true',
            'restime_within_ms': 10000,
            'format': 'txt2_2'
        }
        url = f"{API_URL}?{urllib.parse.urlencode(params)}"
        response = urllib.request.urlopen(url)
        return response.read().decode('utf-8')

    def crawl(self):
        """爬取所有协议的代理"""
        for scheme in SCHEMES:
            try:
                proxy_text = self._get_proxies(scheme)
                if not proxy_text:
                    continue
                
                for line in proxy_text.strip().split('\n'):
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        proxy = convert_proxy_or_proxies(line)
                        if proxy:
                            yield proxy
                    except Exception as e:
                        logger.debug(f'解析代理失败 [{scheme}] {line}: {e}')
                
            except Exception as e:
                logger.error(f'{scheme} 协议爬取失败: {e}')
