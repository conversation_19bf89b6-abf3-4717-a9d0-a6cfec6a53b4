#!/usr/bin/env python3
"""
协议感知代理池系统部署脚本
自动化部署和启动系统
"""

import asyncio
import subprocess
import sys
import os
import time
from pathlib import Path
from loguru import logger

# 导入系统组件
from http_proxy_pool.processors.getter import Getter
from http_proxy_pool.processors.tester import Tester
from http_proxy_pool.storages.protocol_aware_redis_client import ProtocolAwareRedisClient


class DeploymentManager:
    """部署管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.processes = {}
    
    def check_dependencies(self):
        """检查依赖"""
        logger.info("🔍 检查系统依赖...")
        
        required_packages = [
            ('redis', 'redis'),
            ('aiohttp', 'aiohttp'),
            ('aiohttp-socks', 'aiohttp_socks'),
            ('fastapi', 'fastapi'),
            ('uvicorn', 'uvicorn'),
            ('loguru', 'loguru'),
            ('attrs', 'attr')  # attrs包的导入名是attr
        ]
        
        missing_packages = []
        for package_name, import_name in required_packages:
            try:
                __import__(import_name)
            except ImportError:
                missing_packages.append(package_name)
        
        if missing_packages:
            logger.error(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
            logger.info("请运行: pip install " + " ".join(missing_packages))
            return False
        
        logger.info("✅ 所有依赖包已安装")
        return True
    
    async def check_redis_connection(self):
        """检查Redis连接"""
        logger.info("🔗 检查Redis连接...")
        
        try:
            client = ProtocolAwareRedisClient()
            counts = await client.count_by_protocol()
            await client.close()
            
            logger.info("✅ Redis连接正常")
            logger.info(f"当前代理统计: {counts}")
            return True
        except Exception as e:
            logger.error(f"❌ Redis连接失败: {e}")
            logger.info("请确保Redis服务正在运行")
            return False
    
    def create_systemd_service(self):
        """创建systemd服务文件"""
        logger.info("📝 创建systemd服务文件...")
        
        service_template = f"""[Unit]
Description=Protocol Aware Proxy Pool System
After=network.target redis.service
Requires=redis.service

[Service]
Type=simple
User=proxy-pool
Group=proxy-pool
WorkingDirectory={self.project_root}
Environment=PYTHONPATH={self.project_root}

# API服务器
ExecStart=/usr/bin/python3 -m http_proxy_pool.processors.protocol_aware_server
Restart=always
RestartSec=10

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=proxy-pool-api

[Install]
WantedBy=multi-user.target
"""
        
        getter_service_template = f"""[Unit]
Description=Protocol Aware Proxy Pool Getter
After=network.target redis.service proxy-pool-api.service
Requires=redis.service

[Service]
Type=simple
User=proxy-pool
Group=proxy-pool
WorkingDirectory={self.project_root}
Environment=PYTHONPATH={self.project_root}

# Getter服务
ExecStart=/usr/bin/python3 -c "
import asyncio
from http_proxy_pool.processors.getter import Getter

async def run_getter():
    while True:
        try:
            getter = Getter()
            await getter.run()
            await getter.redis.close()
            await asyncio.sleep(300)  # 5分钟间隔
        except Exception as e:
            print(f'Getter error: {{e}}')
            await asyncio.sleep(60)   # 错误后1分钟重试

asyncio.run(run_getter())
"

Restart=always
RestartSec=30

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=proxy-pool-getter

[Install]
WantedBy=multi-user.target
"""
        
        tester_service_template = f"""[Unit]
Description=Protocol Aware Proxy Pool Tester
After=network.target redis.service proxy-pool-api.service
Requires=redis.service

[Service]
Type=simple
User=proxy-pool
Group=proxy-pool
WorkingDirectory={self.project_root}
Environment=PYTHONPATH={self.project_root}

# Tester服务
ExecStart=/usr/bin/python3 -c "
import asyncio
from http_proxy_pool.processors.tester import Tester

async def run_tester():
    while True:
        try:
            tester = Tester()
            await tester.run()
            await asyncio.sleep(600)  # 10分钟间隔
        except Exception as e:
            print(f'Tester error: {{e}}')
            await asyncio.sleep(120)  # 错误后2分钟重试

asyncio.run(run_tester())
"

Restart=always
RestartSec=60

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=proxy-pool-tester

[Install]
WantedBy=multi-user.target
"""
        
        services = {
            'proxy-pool-api.service': service_template,
            'proxy-pool-getter.service': getter_service_template,
            'proxy-pool-tester.service': tester_service_template,
        }
        
        service_dir = self.project_root / 'systemd'
        service_dir.mkdir(exist_ok=True)
        
        for service_name, content in services.items():
            service_file = service_dir / service_name
            with open(service_file, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"✅ 创建服务文件: {service_file}")
        
        logger.info("📋 部署systemd服务的命令:")
        logger.info("sudo cp systemd/*.service /etc/systemd/system/")
        logger.info("sudo systemctl daemon-reload")
        logger.info("sudo systemctl enable proxy-pool-api proxy-pool-getter proxy-pool-tester")
        logger.info("sudo systemctl start proxy-pool-api proxy-pool-getter proxy-pool-tester")
    
    def create_docker_compose(self):
        """创建Docker Compose配置"""
        logger.info("🐳 创建Docker Compose配置...")
        
        docker_compose_template = f"""version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: proxy-pool-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  proxy-pool-api:
    build: .
    container_name: proxy-pool-api
    restart: unless-stopped
    ports:
      - "5555:5555"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - API_HOST=0.0.0.0
      - API_PORT=5555
    depends_on:
      redis:
        condition: service_healthy
    command: python -m http_proxy_pool.processors.protocol_aware_server
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5555/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  proxy-pool-getter:
    build: .
    container_name: proxy-pool-getter
    restart: unless-stopped
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      redis:
        condition: service_healthy
      proxy-pool-api:
        condition: service_healthy
    command: python -c "
import asyncio
from http_proxy_pool.processors.getter import Getter

async def run_getter():
    while True:
        try:
            getter = Getter()
            await getter.run()
            await getter.redis.close()
            await asyncio.sleep(300)
        except Exception as e:
            print(f'Getter error: {{e}}')
            await asyncio.sleep(60)

asyncio.run(run_getter())
"

  proxy-pool-tester:
    build: .
    container_name: proxy-pool-tester
    restart: unless-stopped
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      redis:
        condition: service_healthy
      proxy-pool-api:
        condition: service_healthy
    command: python -c "
import asyncio
from http_proxy_pool.processors.tester import Tester

async def run_tester():
    while True:
        try:
            tester = Tester()
            await tester.run()
            await asyncio.sleep(600)
        except Exception as e:
            print(f'Tester error: {{e}}')
            await asyncio.sleep(120)

asyncio.run(run_tester())
"

volumes:
  redis_data:
    driver: local

networks:
  default:
    name: proxy-pool-network
"""
        
        dockerfile_template = """FROM python:3.12-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd --create-home --shell /bin/bash proxy-pool
RUN chown -R proxy-pool:proxy-pool /app
USER proxy-pool

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:5555/health || exit 1

# 默认命令
CMD ["python", "-m", "http_proxy_pool.processors.protocol_aware_server"]
"""
        
        # 写入文件
        with open(self.project_root / 'docker-compose.yml', 'w', encoding='utf-8') as f:
            f.write(docker_compose_template)
        
        with open(self.project_root / 'Dockerfile', 'w', encoding='utf-8') as f:
            f.write(dockerfile_template)
        
        logger.info("✅ 创建Docker配置文件")
        logger.info("📋 Docker部署命令:")
        logger.info("docker-compose up -d")
    
    def create_startup_script(self):
        """创建启动脚本"""
        logger.info("📜 创建启动脚本...")
        
        startup_script = f"""#!/bin/bash

# 协议感知代理池系统启动脚本

set -e

PROJECT_ROOT="{self.project_root}"
cd "$PROJECT_ROOT"

echo "🚀 启动协议感知代理池系统..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 检查Redis
if ! command -v redis-cli &> /dev/null; then
    echo "⚠️ Redis CLI 未安装，无法检查Redis状态"
else
    if ! redis-cli ping &> /dev/null; then
        echo "❌ Redis 未运行，请先启动Redis服务"
        exit 1
    fi
    echo "✅ Redis 连接正常"
fi

# 设置环境变量
export PYTHONPATH="$PROJECT_ROOT"

# 启动API服务器（后台）
echo "🌐 启动API服务器..."
nohup python3 -m http_proxy_pool.processors.protocol_aware_server > logs/api.log 2>&1 &
API_PID=$!
echo $API_PID > pids/api.pid
echo "✅ API服务器已启动 (PID: $API_PID)"

# 等待API服务器启动
sleep 5

# 启动Getter（后台）
echo "🕷️ 启动代理获取器..."
nohup python3 -c "
import asyncio
import time
from http_proxy_pool.processors.getter import Getter

async def run_getter():
    while True:
        try:
            print(f'[{{time.strftime(\"%Y-%m-%d %H:%M:%S\")}}] 开始获取代理...')
            getter = Getter()
            await getter.run()
            await getter.redis.close()
            print(f'[{{time.strftime(\"%Y-%m-%d %H:%M:%S\")}}] 代理获取完成，等待5分钟...')
            await asyncio.sleep(300)
        except Exception as e:
            print(f'[{{time.strftime(\"%Y-%m-%d %H:%M:%S\")}}] Getter错误: {{e}}')
            await asyncio.sleep(60)

asyncio.run(run_getter())
" > logs/getter.log 2>&1 &
GETTER_PID=$!
echo $GETTER_PID > pids/getter.pid
echo "✅ 代理获取器已启动 (PID: $GETTER_PID)"

# 启动Tester（后台）
echo "🧪 启动代理测试器..."
nohup python3 -c "
import asyncio
import time
from http_proxy_pool.processors.tester import Tester

async def run_tester():
    while True:
        try:
            print(f'[{{time.strftime(\"%Y-%m-%d %H:%M:%S\")}}] 开始测试代理...')
            tester = Tester()
            await tester.run()
            print(f'[{{time.strftime(\"%Y-%m-%d %H:%M:%S\")}}] 代理测试完成，等待10分钟...')
            await asyncio.sleep(600)
        except Exception as e:
            print(f'[{{time.strftime(\"%Y-%m-%d %H:%M:%S\")}}] Tester错误: {{e}}')
            await asyncio.sleep(120)

asyncio.run(run_tester())
" > logs/tester.log 2>&1 &
TESTER_PID=$!
echo $TESTER_PID > pids/tester.pid
echo "✅ 代理测试器已启动 (PID: $TESTER_PID)"

echo ""
echo "🎉 协议感知代理池系统启动完成！"
echo ""
echo "📊 服务状态:"
echo "  API服务器: http://localhost:5555 (PID: $API_PID)"
echo "  代理获取器: PID $GETTER_PID"
echo "  代理测试器: PID $TESTER_PID"
echo ""
echo "📋 管理命令:"
echo "  查看API日志: tail -f logs/api.log"
echo "  查看获取器日志: tail -f logs/getter.log"
echo "  查看测试器日志: tail -f logs/tester.log"
echo "  停止系统: ./stop.sh"
echo "  系统状态: ./status.sh"
echo ""
echo "🌐 API接口:"
echo "  健康检查: curl http://localhost:5555/health"
echo "  获取代理: curl http://localhost:5555/random"
echo "  协议统计: curl http://localhost:5555/stats"
"""
        
        stop_script = f"""#!/bin/bash

# 协议感知代理池系统停止脚本

PROJECT_ROOT="{self.project_root}"
cd "$PROJECT_ROOT"

echo "🛑 停止协议感知代理池系统..."

# 停止各个服务
for service in api getter tester; do
    if [ -f "pids/$service.pid" ]; then
        PID=$(cat "pids/$service.pid")
        if kill -0 $PID 2>/dev/null; then
            echo "停止 $service (PID: $PID)..."
            kill $PID
            sleep 2
            if kill -0 $PID 2>/dev/null; then
                echo "强制停止 $service..."
                kill -9 $PID
            fi
        fi
        rm -f "pids/$service.pid"
    fi
done

echo "✅ 系统已停止"
"""
        
        status_script = f"""#!/bin/bash

# 协议感知代理池系统状态检查脚本

PROJECT_ROOT="{self.project_root}"
cd "$PROJECT_ROOT"

echo "📊 协议感知代理池系统状态"
echo "=" * 40

# 检查各个服务状态
for service in api getter tester; do
    if [ -f "pids/$service.pid" ]; then
        PID=$(cat "pids/$service.pid")
        if kill -0 $PID 2>/dev/null; then
            echo "✅ $service: 运行中 (PID: $PID)"
        else
            echo "❌ $service: 已停止"
        fi
    else
        echo "❌ $service: 未启动"
    fi
done

echo ""

# 检查API服务器
if curl -s http://localhost:5555/health > /dev/null 2>&1; then
    echo "✅ API服务器: 可访问"
    echo "📈 系统统计:"
    curl -s http://localhost:5555/stats | python3 -m json.tool 2>/dev/null || echo "无法获取统计信息"
else
    echo "❌ API服务器: 不可访问"
fi
"""
        
        # 创建目录
        (self.project_root / 'logs').mkdir(exist_ok=True)
        (self.project_root / 'pids').mkdir(exist_ok=True)
        
        # 写入脚本文件
        scripts = {
            'start.sh': startup_script,
            'stop.sh': stop_script,
            'status.sh': status_script,
        }
        
        for script_name, content in scripts.items():
            script_file = self.project_root / script_name
            with open(script_file, 'w', encoding='utf-8') as f:
                f.write(content)
            os.chmod(script_file, 0o755)  # 添加执行权限
            logger.info(f"✅ 创建脚本: {script_file}")
    
    async def run_deployment(self):
        """运行部署"""
        logger.info("🚀 开始部署协议感知代理池系统...")
        
        # 检查依赖
        if not self.check_dependencies():
            return False
        
        # 检查Redis连接
        if not await self.check_redis_connection():
            return False
        
        # 创建部署文件
        self.create_systemd_service()
        self.create_docker_compose()
        self.create_startup_script()
        
        logger.info("✅ 部署文件创建完成")
        logger.info("")
        logger.info("📋 部署选项:")
        logger.info("1. 直接启动: ./start.sh")
        logger.info("2. Docker部署: docker-compose up -d")
        logger.info("3. Systemd部署: 参考systemd/目录下的说明")
        logger.info("")
        logger.info("🎯 部署完成后访问:")
        logger.info("  API文档: http://localhost:5555")
        logger.info("  健康检查: http://localhost:5555/health")
        logger.info("  获取代理: http://localhost:5555/random")
        
        return True


async def main():
    """主函数"""
    logger.info("🎯 协议感知代理池系统部署工具")
    logger.info("=" * 50)
    
    manager = DeploymentManager()
    
    try:
        success = await manager.run_deployment()
        
        if success:
            logger.info("🎉 部署准备完成！")
            sys.exit(0)
        else:
            logger.error("❌ 部署失败！")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"💥 部署过程中发生错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())