# encoding: utf-8
import urllib3
from loguru import logger
from http_proxy_pool.crawlers.base import BaseCrawler
from http_proxy_pool.schemas.proxy import Proxy

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
# Max proxies to collect from each individual source
# If the source has too much proxies, then likely many of these don't work
# Set to 0 to disable this limit
max_proxies_per_source = 1000
# The number of seconds to wait for the source to be fetched
timeout = 5.0

# 分协议的代理源URL配置
HTTP_URLS = [
    'https://gist.githubusercontent.com/yyh357/566fc1afc659e42df1186785775991f6/raw/082ae98d2a434ae2b6880209850080df73e9533b/gistfile1.txt',
    "https://api.proxyscrape.com/v3/free-proxy-list/get?request=getproxies&protocol=http",
    "https://api.proxyscrape.com/v3/free-proxy-list/get?request=getproxies&protocol=https",
    "https://api.openproxylist.xyz/http.txt",
    "https://api.proxyscrape.com/v2/?request=getproxies&protocol=http",
    "https://api.proxyscrape.com/v2/?request=getproxies&protocol=https",
    "https://proxyspace.pro/http.txt",
    "https://proxyspace.pro/https.txt",
    "http://pubproxy.com/api/proxy?limit=5&format=txt&type=http&level=anonymous&last_check=60&no_country=CN",
    "https://raw.githubusercontent.com/roosterkid/openproxylist/main/HTTPS.txt",
    "https://raw.githubusercontent.com/zloi-user/hideip.me/refs/heads/master/http.txt",
    "https://raw.githubusercontent.com/zloi-user/hideip.me/refs/heads/master/https.txt",
    "https://raw.githubusercontent.com/proxifly/free-proxy-list/refs/heads/main/proxies/protocols/http/data.txt",
    "https://raw.githubusercontent.com/proxifly/free-proxy-list/refs/heads/main/proxies/protocols/https/data.txt",
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/http.txt",
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/https.txt",
    "https://raw.githubusercontent.com/monosans/proxy-list/refs/heads/main/proxies/http.txt",
    "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/refs/heads/master/http.txt",
    "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/refs/heads/main/proxy_files/http_proxies.txt",
    "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/refs/heads/main/proxy_files/https_proxies.txt",
    "https://raw.githubusercontent.com/vmheaven/VMHeaven-Free-Proxy-Updated/refs/heads/main/http.txt",
    "https://raw.githubusercontent.com/vmheaven/VMHeaven-Free-Proxy-Updated/refs/heads/main/https.txt",
    "https://raw.githubusercontent.com/sunny9577/proxy-scraper/refs/heads/master/generated/http_proxies.txt",
    "https://raw.githubusercontent.com/AlestackOverglow/proxy-list/refs/heads/main/proxies_with_protocol.txt",
    "https://raw.githubusercontent.com/ALIILAPRO/Proxy/refs/heads/main/http.txt",
    "https://raw.githubusercontent.com/andigwandi/free-proxy/refs/heads/main/proxy_list.txt",
    "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/refs/heads/main/proxy_files/http_proxies.txt",
    "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/refs/heads/main/proxy_files/https_proxies.txt",
    "https://raw.githubusercontent.com/aslisk/proxyhttps/refs/heads/main/https.txt",
    "https://raw.githubusercontent.com/dinoz0rg/proxy-list/refs/heads/main/checked_proxies/http.txt",
    "https://raw.githubusercontent.com/dpangestuw/Free-Proxy/refs/heads/main/http_proxies.txt",
    "https://raw.githubusercontent.com/elliottophellia/proxylist/refs/heads/master/results/http/global/http_checked.txt",
    "https://raw.githubusercontent.com/ErcinDedeoglu/proxies/refs/heads/main/proxies/http.txt",
    "https://raw.githubusercontent.com/ErcinDedeoglu/proxies/refs/heads/main/proxies/https.txt",
    "https://raw.githubusercontent.com/hendrikbgr/Free-Proxy-Repo/refs/heads/master/proxy_list.txt",
    "https://raw.githubusercontent.com/mmpx12/proxy-list/refs/heads/master/http.txt",
    "https://raw.githubusercontent.com/mmpx12/proxy-list/refs/heads/master/https.txt",
    "https://raw.githubusercontent.com/MrMarble/proxy-list/refs/heads/main/all.txt",
    "https://raw.githubusercontent.com/MuRongPIG/Proxy-Master/refs/heads/main/http_checked.txt",
    "https://raw.githubusercontent.com/Noctiro/getproxy/refs/heads/master/file/http.txt",
    "https://raw.githubusercontent.com/Noctiro/getproxy/refs/heads/master/file/https.txt",
    "https://raw.githubusercontent.com/officialputuid/KangProxy/refs/heads/KangProxy/http/http.txt",
    "https://raw.githubusercontent.com/officialputuid/KangProxy/refs/heads/KangProxy/https/https.txt",
    "https://raw.githubusercontent.com/r00tee/Proxy-List/refs/heads/main/Https.txt",
    "https://raw.githubusercontent.com/roosterkid/openproxylist/refs/heads/main/HTTPS_RAW.txt",
    "https://raw.githubusercontent.com/saisuiu/Lionkings-Http-Proxys-Proxies/refs/heads/main/free.txt",
    "https://raw.githubusercontent.com/SevenworksDev/proxy-list/refs/heads/main/proxies/http.txt",
    "https://raw.githubusercontent.com/SevenworksDev/proxy-list/refs/heads/main/proxies/https.txt",
    "https://raw.githubusercontent.com/Skiddle-ID/proxylist/refs/heads/main/proxies.txt",
    "https://raw.githubusercontent.com/SoliSpirit/proxy-list/refs/heads/main/http.txt",
    "https://raw.githubusercontent.com/SoliSpirit/proxy-list/refs/heads/main/https.txt",
    "https://raw.githubusercontent.com/sunny9577/proxy-scraper/refs/heads/master/generated/http_proxies.txt",
    "https://raw.githubusercontent.com/themiralay/Proxy-List-World/refs/heads/master/data.txt",
    "https://raw.githubusercontent.com/Tsprnay/Proxy-lists/refs/heads/master/proxies/http.txt",
    "https://raw.githubusercontent.com/Tsprnay/Proxy-lists/refs/heads/master/proxies/https.txt",
    "https://raw.githubusercontent.com/tuanminpay/live-proxy/refs/heads/master/http.txt",
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/http.txt",
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/https.txt",
    "https://raw.githubusercontent.com/Vann-Dev/proxy-list/refs/heads/main/proxies/http.txt",
    "https://raw.githubusercontent.com/Vann-Dev/proxy-list/refs/heads/main/proxies/https.txt",
    "https://raw.githubusercontent.com/vmheaven/VMHeaven-Free-Proxy-Updated/refs/heads/main/http.txt",
    "https://raw.githubusercontent.com/vmheaven/VMHeaven-Free-Proxy-Updated/refs/heads/main/https.txt",
    "https://raw.githubusercontent.com/yemixzy/proxy-list/refs/heads/main/proxies/http.txt",
    "https://raw.githubusercontent.com/Zaeem20/FREE_PROXIES_LIST/refs/heads/master/http.txt",
    "https://raw.githubusercontent.com/Zaeem20/FREE_PROXIES_LIST/refs/heads/master/https.txt",
    "https://raw.githubusercontent.com/zevtyardt/proxy-list/refs/heads/main/http.txt",
    "https://raw.githubusercontent.com/zloi-user/hideip.me/refs/heads/master/http.txt",
    "https://raw.githubusercontent.com/zloi-user/hideip.me/refs/heads/master/https.txt",
    "https://raw.githubusercontent.com/ZoniBoy00/proxy-lists/refs/heads/master/http_proxies.txt",
    "https://www.proxy-list.download/api/v1/get?type=http",
    "https://www.proxy-list.download/api/v1/get?type=https",
]

SOCKS4_URLS = [
    "https://api.proxyscrape.com/v3/free-proxy-list/get?request=getproxies&protocol=socks4",
    "https://api.openproxylist.xyz/socks4.txt",
    "https://api.proxyscrape.com/v2/?request=getproxies&protocol=socks4",
    "https://proxyspace.pro/socks4.txt",
    "https://raw.githubusercontent.com/roosterkid/openproxylist/main/SOCKS4.txt",
    "https://raw.githubusercontent.com/zloi-user/hideip.me/refs/heads/master/socks4.txt",
    "https://raw.githubusercontent.com/proxifly/free-proxy-list/refs/heads/main/proxies/protocols/socks4/data.txt",
    "https://raw.githubusercontent.com/ALIILAPRO/Proxy/refs/heads/main/socks4.txt",
    "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/refs/heads/main/proxy_files/socks4_proxies.txt",
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/socks4.txt",
    "https://raw.githubusercontent.com/monosans/proxy-list/refs/heads/main/proxies/socks4.txt",
    "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/refs/heads/master/socks4.txt",
    "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/refs/heads/main/proxy_files/socks4_proxies.txt",
    "https://raw.githubusercontent.com/vmheaven/VMHeaven-Free-Proxy-Updated/refs/heads/main/socks4.txt",
    "https://raw.githubusercontent.com/dinoz0rg/proxy-list/refs/heads/main/checked_proxies/socks4.txt",
    "https://raw.githubusercontent.com/dpangestuw/Free-Proxy/refs/heads/main/socks4_proxies.txt",
    "https://raw.githubusercontent.com/elliottophellia/proxylist/refs/heads/master/results/socks4/global/socks4_checked.txt",
    "https://raw.githubusercontent.com/ErcinDedeoglu/proxies/refs/heads/main/proxies/socks4.txt",
    "https://raw.githubusercontent.com/mmpx12/proxy-list/refs/heads/master/socks4.txt",
    "https://raw.githubusercontent.com/MuRongPIG/Proxy-Master/refs/heads/main/socks4_checked.txt",
    "https://raw.githubusercontent.com/Noctiro/getproxy/refs/heads/master/file/socks4.txt",
    "https://raw.githubusercontent.com/officialputuid/KangProxy/refs/heads/KangProxy/socks4/socks4.txt",
    "https://raw.githubusercontent.com/r00tee/Proxy-List/refs/heads/main/Socks4.txt",
    "https://raw.githubusercontent.com/roosterkid/openproxylist/refs/heads/main/SOCKS4_RAW.txt",
    "https://raw.githubusercontent.com/SevenworksDev/proxy-list/refs/heads/main/proxies/socks4.txt",
    "https://raw.githubusercontent.com/SoliSpirit/proxy-list/refs/heads/main/socks4.txt",
    "https://raw.githubusercontent.com/sunny9577/proxy-scraper/refs/heads/master/generated/socks4_proxies.txt",
    "https://raw.githubusercontent.com/Tsprnay/Proxy-lists/refs/heads/master/proxies/socks4.txt",
    "https://raw.githubusercontent.com/tuanminpay/live-proxy/refs/heads/master/socks4.txt",
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/socks4.txt",
    "https://raw.githubusercontent.com/Vann-Dev/proxy-list/refs/heads/main/proxies/socks4.txt",
    "https://raw.githubusercontent.com/vmheaven/VMHeaven-Free-Proxy-Updated/refs/heads/main/socks4.txt",
    "https://raw.githubusercontent.com/yemixzy/proxy-list/refs/heads/main/proxies/socks4.txt",
    "https://raw.githubusercontent.com/Zaeem20/FREE_PROXIES_LIST/refs/heads/master/socks4.txt",
    "https://raw.githubusercontent.com/zevtyardt/proxy-list/refs/heads/main/socks4.txt",
    "https://raw.githubusercontent.com/zloi-user/hideip.me/refs/heads/master/socks4.txt",
    "https://raw.githubusercontent.com/ZoniBoy00/proxy-lists/refs/heads/master/socks4_proxies.txt",
    "https://www.proxy-list.download/api/v1/get?type=socks4",
]

SOCKS5_URLS = [
    "https://api.proxyscrape.com/v3/free-proxy-list/get?request=getproxies&protocol=socks5",
    "https://api.openproxylist.xyz/socks5.txt",
    "https://api.proxyscrape.com/v2/?request=getproxies&protocol=socks5",
    "https://proxyspace.pro/socks5.txt",
    "https://raw.githubusercontent.com/roosterkid/openproxylist/main/SOCKS5.txt",
    "https://raw.githubusercontent.com/zloi-user/hideip.me/refs/heads/master/socks5.txt",
    "https://raw.githubusercontent.com/proxifly/free-proxy-list/refs/heads/main/proxies/protocols/socks5/data.txt",
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/socks5.txt",
    "https://raw.githubusercontent.com/monosans/proxy-list/refs/heads/main/proxies/socks5.txt",
    "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/refs/heads/master/socks5.txt",
    "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/refs/heads/main/proxy_files/socks5_proxies.txt",
    "https://raw.githubusercontent.com/vmheaven/VMHeaven-Free-Proxy-Updated/refs/heads/main/socks5.txt",
    "https://raw.githubusercontent.com/ALIILAPRO/Proxy/refs/heads/main/socks5.txt",
    "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/refs/heads/main/proxy_files/socks5_proxies.txt",
    "https://raw.githubusercontent.com/dinoz0rg/proxy-list/refs/heads/main/checked_proxies/socks5.txt",
    "https://raw.githubusercontent.com/dpangestuw/Free-Proxy/refs/heads/main/socks5_proxies.txt",
    "https://raw.githubusercontent.com/elliottophellia/proxylist/refs/heads/master/results/socks5/global/socks5_checked.txt",
    "https://raw.githubusercontent.com/ErcinDedeoglu/proxies/refs/heads/main/proxies/socks5.txt",
    "https://raw.githubusercontent.com/hookzof/socks5_list/refs/heads/master/proxy.txt",
    "https://raw.githubusercontent.com/mmpx12/proxy-list/refs/heads/master/socks5.txt",
    "https://raw.githubusercontent.com/MuRongPIG/Proxy-Master/refs/heads/main/socks5_checked.txt",
    "https://raw.githubusercontent.com/Noctiro/getproxy/refs/heads/master/file/socks5.txt",
    "https://raw.githubusercontent.com/officialputuid/KangProxy/refs/heads/KangProxy/socks5/socks5.txt",
    "https://raw.githubusercontent.com/r00tee/Proxy-List/refs/heads/main/Socks5.txt",
    "https://raw.githubusercontent.com/roosterkid/openproxylist/refs/heads/main/SOCKS5_RAW.txt",
    "https://raw.githubusercontent.com/SevenworksDev/proxy-list/refs/heads/main/proxies/socks5.txt",
    "https://raw.githubusercontent.com/SoliSpirit/proxy-list/refs/heads/main/socks5.txt",
    "https://raw.githubusercontent.com/sunny9577/proxy-scraper/refs/heads/master/generated/socks5_proxies.txt",
    "https://raw.githubusercontent.com/Tsprnay/Proxy-lists/refs/heads/master/proxies/socks5.txt",
    "https://raw.githubusercontent.com/tuanminpay/live-proxy/refs/heads/master/socks5.txt",
    "https://raw.githubusercontent.com/vakhov/fresh-proxy-list/refs/heads/master/socks5.txt",
    "https://raw.githubusercontent.com/Vann-Dev/proxy-list/refs/heads/main/proxies/socks5.txt",
    "https://raw.githubusercontent.com/vmheaven/VMHeaven-Free-Proxy-Updated/refs/heads/main/socks5.txt",
    "https://raw.githubusercontent.com/yemixzy/proxy-list/refs/heads/main/proxies/socks5.txt",
    "https://raw.githubusercontent.com/Zaeem20/FREE_PROXIES_LIST/refs/heads/master/socks5.txt",
    "https://raw.githubusercontent.com/zevtyardt/proxy-list/refs/heads/main/socks5.txt",
    "https://raw.githubusercontent.com/zloi-user/hideip.me/refs/heads/master/socks5.txt",
    "https://raw.githubusercontent.com/ZoniBoy00/proxy-lists/refs/heads/master/socks5_proxies.txt",
    "https://www.proxy-list.download/api/v1/get?type=socks5",
]


class TXTProxiesCrawler(BaseCrawler):
    """TXT代理列表爬虫 - 支持协议分类"""

    def __init__(self):
        # 构建URL和协议的映射
        self.url_protocol_map = {}
        
        # HTTP协议URL
        for url in HTTP_URLS:
            self.url_protocol_map[url] = 'http'
        
        # SOCKS4协议URL
        for url in SOCKS4_URLS:
            self.url_protocol_map[url] = 'socks4'
        
        # SOCKS5协议URL
        for url in SOCKS5_URLS:
            self.url_protocol_map[url] = 'socks5'
        
        # 设置所有URL
        self.urls = list(self.url_protocol_map.keys())

    def fetch(self, url):
        """重写fetch方法以使用自定义timeout，并处理API频率限制"""
        import time
        import random
        
        # 对于特定API，添加预延迟避免频率限制
        if 'proxy-list.download' in url:
            delay = random.uniform(1.0, 3.0)
            time.sleep(delay)
        elif 'openproxy.space' in url:
            delay = random.uniform(0.5, 2.0)
            time.sleep(delay)
        elif 'api.proxyscrape.com' in url:
            delay = random.uniform(0.5, 1.5)
            time.sleep(delay)
        
        # 使用基类的改进版本，增加超时时间
        return self._fetch_with_429_handling(url, timeout=timeout)
    
    def crawl(self):
        """
        重写crawl方法，解决RetryError中断问题，添加并行处理
        """
        import concurrent.futures
        import threading
        from retrying import RetryError
        
        def fetch_and_process(url):
            """获取并处理单个URL的代理"""
            try:
                logger.debug(f'开始获取 {url}')
                html = self.fetch(url)
                if not html:
                    logger.warning(f'URL返回空内容: {url}')
                    return []
                
                # 收集该URL的所有代理
                proxies = []
                for proxy in self.process(html, url):
                    proxies.append(proxy)
                
                return proxies
                
            except RetryError:
                logger.error(f'URL获取失败（重试3次后放弃）: {url}')
                return []
            except Exception as e:
                logger.error(f'处理URL时出错 {url}: {e}')
                return []
        
        # 使用线程池并行处理URL
        max_workers = min(10, len(self.urls))  # 最多10个并发线程
        logger.debug(f'开始并行获取 {len(self.urls)} 个URL，使用 {max_workers} 个线程')
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_url = {executor.submit(fetch_and_process, url): url for url in self.urls}
            
            # 处理完成的任务
            for future in concurrent.futures.as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    proxies = future.result()
                    # 逐个yield代理
                    for proxy in proxies:
                        yield proxy
                except Exception as e:
                    logger.error(f'处理URL结果时出错 {url}: {e}')

    def process(self, html, url):
        """处理HTML内容，提取代理"""
        if not html or not url:
            return
        
        # 根据URL确定协议
        protocol = self.url_protocol_map.get(url, 'http')
        
        # 先统计该URL中的代理总数
        lines = [line.strip() for line in html.strip().split('\n') 
                if line.strip() and not line.strip().startswith('#')]
        
        total_lines = len(lines)
        
        # 如果设置了max_proxies_per_source限制且该URL代理数量超过限制，则跳过整个URL
        if max_proxies_per_source > 0 and total_lines > max_proxies_per_source:
            logger.info(f'跳过URL {url}，代理数量 {total_lines} 超过限制 {max_proxies_per_source}')
            return
        
        proxy_count = 0
        # 处理代理
        for line in lines:
            try:
                # 清理行内容，移除可能的额外信息（如国家信息）
                cleaned_line = self._clean_proxy_line(line)
                if not cleaned_line:
                    continue
                
                # 如果已经有协议前缀，直接解析
                if cleaned_line.startswith(('http://', 'https://', 'socks4://', 'socks5://')):
                    proxy = Proxy.from_string(cleaned_line)
                else:
                    # 纯ip:port格式，根据URL来源添加协议前缀
                    proxy_with_protocol = f'{protocol}://{cleaned_line}'
                    proxy = Proxy.from_string(proxy_with_protocol)
                
                if proxy and proxy.validate():
                    proxy_count += 1
                    yield proxy
                    
            except Exception as e:
                logger.debug(f'解析代理失败: {line} - {e}')
        
        logger.info(f'从 {url} 获取到 {proxy_count} 个代理')

    def _clean_proxy_line(self, line):
        """清理代理行，移除额外信息，支持多种格式"""
        if not line:
            return None
        
        # 移除首尾空白
        line = line.strip()
        
        # 跳过注释行和空行
        if not line or line.startswith('#') or line.startswith('//'):
            return None
        
        # 处理JSON格式 (如: {"ip":"*******","port":"8080"})
        if line.startswith('{') and line.endswith('}'):
            try:
                import json
                data = json.loads(line)
                if 'ip' in data and 'port' in data:
                    return f"{data['ip']}:{data['port']}"
            except:
                pass
        
        # 处理CSV格式 (如: *******,8080,country)
        if ',' in line and ':' not in line:
            parts = line.split(',')
            if len(parts) >= 2:
                try:
                    ip = parts[0].strip()
                    port = int(parts[1].strip())
                    if 1 <= port <= 65535:
                        return f"{ip}:{port}"
                except:
                    pass
        
        # 处理制表符分隔 (如: *******\t8080)
        if '\t' in line:
            parts = line.split('\t')
            if len(parts) >= 2:
                try:
                    ip = parts[0].strip()
                    port = int(parts[1].strip())
                    if 1 <= port <= 65535:
                        return f"{ip}:{port}"
                except:
                    pass
        
        # 处理空格分隔 (如: ******* 8080)
        if ' ' in line and ':' not in line:
            parts = line.split()
            if len(parts) >= 2:
                try:
                    ip = parts[0].strip()
                    port = int(parts[1].strip())
                    if 1 <= port <= 65535:
                        return f"{ip}:{port}"
                except:
                    pass
        
        # 处理带协议前缀的格式 (如: http://*******:8080)
        if line.startswith(('http://', 'https://', 'socks4://', 'socks5://')):
            return line
        
        # 处理标准 ip:port 格式
        if ':' in line:
            # 移除可能的国家信息（格式：ip:port:country 或 ip:port:country:other）
            parts = line.split(':')
            if len(parts) >= 2:
                try:
                    ip = parts[0].strip()
                    port = int(parts[1].strip())
                    
                    # 验证端口范围
                    if not (1 <= port <= 65535):
                        return None
                    
                    # 验证IP格式（简单检查）
                    ip_parts = ip.split('.')
                    if len(ip_parts) == 4:
                        for part in ip_parts:
                            if not part.isdigit() or not (0 <= int(part) <= 255):
                                return None
                        return f"{ip}:{port}"
                    
                    # 可能是IPv6或域名，直接返回
                    return f"{ip}:{port}"
                    
                except ValueError:
                    # 如果第二部分不是数字，可能是用户名密码格式
                    # 格式: user:pass@ip:port 或 protocol://user:pass@ip:port
                    if '@' in line:
                        # 提取@后面的ip:port部分
                        at_parts = line.split('@')
                        if len(at_parts) >= 2:
                            ip_port = at_parts[-1]
                            if ':' in ip_port:
                                ip_port_parts = ip_port.split(':')
                                if len(ip_port_parts) >= 2:
                                    try:
                                        port = int(ip_port_parts[-1])
                                        if 1 <= port <= 65535:
                                            return line  # 保持原格式，包含用户名密码
                                    except:
                                        pass
                    return None
        
        return None

    def _detect_proxy_format(self, line):
        """检测代理格式类型"""
        if not line:
            return "empty"
        
        line = line.strip()
        
        if line.startswith('#') or line.startswith('//'):
            return "comment"
        
        if line.startswith('{') and line.endswith('}'):
            return "json"
        
        if ',' in line and ':' not in line:
            return "csv"
        
        if '\t' in line:
            return "tab_separated"
        
        if ' ' in line and ':' not in line:
            return "space_separated"
        
        if line.startswith(('http://', 'https://', 'socks4://', 'socks5://')):
            return "protocol_prefixed"
        
        if '@' in line:
            return "with_auth"
        
        if ':' in line:
            parts = line.split(':')
            if len(parts) == 2:
                return "standard_ip_port"
            elif len(parts) > 2:
                return "ip_port_with_extra"
        
        return "unknown"
