[Unit]
Description=Protocol Aware Proxy Pool Tester
After=network.target redis.service proxy-pool-api.service
Requires=redis.service

[Service]
Type=simple
User=proxy-pool
Group=proxy-pool
WorkingDirectory=D:\onedrive\proxy_pool\http_proxy_pool
Environment=PYTHONPATH=D:\onedrive\proxy_pool\http_proxy_pool

# Tester服务
ExecStart=/usr/bin/python3 -c "
import asyncio
from http_proxy_pool.processors.tester import Tester

async def run_tester():
    while True:
        try:
            tester = Tester()
            await tester.run()
            await asyncio.sleep(600)  # 10分钟间隔
        except Exception as e:
            print(f'Tester error: {e}')
            await asyncio.sleep(120)  # 错误后2分钟重试

asyncio.run(run_tester())
"

Restart=always
RestartSec=60

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=proxy-pool-tester

[Install]
WantedBy=multi-user.target
