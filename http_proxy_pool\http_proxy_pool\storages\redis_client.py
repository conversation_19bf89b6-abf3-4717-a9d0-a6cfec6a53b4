import asyncio
import redis.asyncio as redis
from functools import wraps
from http_proxy_pool.exceptions import PoolEmptyException
from http_proxy_pool.schemas.proxy import Proxy
from http_proxy_pool.setting import REDIS_CONNECTION_STRING, REDIS_HOST, REDIS_PORT, REDIS_PASSWORD, REDIS_DB, REDIS_KEY, PROXY_SCORE_MAX, PROXY_SCORE_MIN, \
    PROXY_SCORE_INIT
from random import choice
from typing import List
from loguru import logger
from http_proxy_pool.utils.proxy import is_valid_proxy, convert_proxy_or_proxies


def ensure_connection(func):
    """装饰器：确保Redis连接已建立"""
    @wraps(func)
    async def wrapper(self, *args, **kwargs):
        await self._ensure_connection()
        return await func(self, *args, **kwargs)
    return wrapper


class AsyncRedisClient:
    """
    异步Redis客户端，用于高性能代理池操作
    """
    
    def __init__(self, host=REDIS_HOST, port=REDIS_PORT, password=REDIS_PASSWORD, db=REDIS_DB,
                 connection_string=REDIS_CONNECTION_STRING, max_connections=50, **kwargs):
        """
        初始化异步Redis客户端
        :param host: redis host
        :param port: redis port
        :param password: redis password
        :param connection_string: redis connection_string
        :param max_connections: 连接池最大连接数
        """
        self._client = None
        self.connection_params = {
            'host': host,
            'port': port,
            'password': password,
            'db': db,
            'connection_string': connection_string,
            'max_connections': max_connections,
            'decode_responses': True,
            **kwargs
        }
    
    def _get_redis_key(self, redis_key=None):
        """获取Redis键，如果为None则使用默认键"""
        return redis_key or REDIS_KEY

    async def _ensure_connection(self):
        """确保Redis连接已建立"""
        if self._client is None:
            # 代理池优化的连接配置
            common_config = {
                'max_connections': min(self.connection_params['max_connections'], 50),  # 限制最大连接数
                'decode_responses': True,
                'socket_connect_timeout': 5,      # 快速连接超时
                'socket_timeout': 10,             # 快速读写超时
                'socket_keepalive': True,
                'socket_keepalive_options': {
                    1: 60,   # TCP_KEEPIDLE: 60秒后开始keepalive
                    2: 10,   # TCP_KEEPINTVL: 每10秒发送一次
                    3: 6,    # TCP_KEEPCNT: 最多6次失败后断开
                },
                'retry_on_timeout': True,
                'retry_on_error': [ConnectionError, TimeoutError],
                'health_check_interval': 10,      # 更频繁的健康检查
                'encoding': 'utf-8',              # 明确编码
                'encoding_errors': 'strict',      # 严格编码错误处理
            }
            
            if self.connection_params['connection_string']:
                self._client = redis.Redis.from_url(
                    self.connection_params['connection_string'],
                    **common_config
                )
            else:
                self._client = redis.Redis(
                    host=self.connection_params['host'],
                    port=self.connection_params['port'],
                    password=self.connection_params['password'],
                    db=self.connection_params['db'],
                    **common_config
                )    
    @ensure_connection
    async def add(self, proxy: Proxy, score=PROXY_SCORE_INIT, redis_key=REDIS_KEY) -> int:
        """
        异步添加代理
        :param proxy: 代理对象
        :param score: 初始分数
        :param redis_key: Redis键
        :return: 添加结果
        """
        if not is_valid_proxy(proxy.string()):
            logger.info(f'invalid proxy {proxy}, throw it')
            return 0
        
        return await self._client.zadd(redis_key, {proxy.string(): score}, nx=True)
    
    @ensure_connection
    async def add_batch(self, proxies: List[Proxy], score=PROXY_SCORE_INIT, redis_key=REDIS_KEY) -> int:
        """
        异步批量添加代理
        :param proxies: 代理列表
        :param score: 初始分数
        :param redis_key: Redis键
        :return: 成功添加的数量
        """
        if not proxies:
            return 0
        
        # 过滤有效代理并构建字典
        proxy_dict = {}
        for proxy in proxies:
            if is_valid_proxy(proxy.string()):
                proxy_dict[proxy.string()] = score
            else:
                logger.info(f'invalid proxy {proxy}, throw it')
        
        return await self._client.zadd(redis_key, proxy_dict, nx=True) if proxy_dict else 0    
    @ensure_connection
    async def random(self, redis_key=None, proxy_score_min=PROXY_SCORE_MIN,
                    proxy_score_max=PROXY_SCORE_MAX) -> Proxy:
        """
        异步获取随机代理（优先获取最高分代理）
        :param redis_key: Redis键，None使用默认键
        :param proxy_score_min: 最小分数
        :param proxy_score_max: 最大分数
        :return: 代理对象
        """
        key = self._get_redis_key(redis_key)

        # 优先获取最高分代理
        proxies = await self._client.zrangebyscore(key, proxy_score_max, proxy_score_max)
        if proxies:
            return convert_proxy_or_proxies(choice(proxies))

        # 否则按分数倒序获取（高分优先）
        proxies = await self._client.zrevrangebyscore(key, proxy_score_max, proxy_score_min)
        if proxies:
            return convert_proxy_or_proxies(choice(proxies))

        raise PoolEmptyException
    
    @ensure_connection
    async def all(self, redis_key=None, proxy_score_min=PROXY_SCORE_MIN,
                 proxy_score_max=PROXY_SCORE_MAX) -> List[Proxy]:
        """
        异步获取所有代理
        :param redis_key: Redis键，None使用默认键
        :param proxy_score_min: 最小分数
        :param proxy_score_max: 最大分数
        :return: 代理列表
        """
        key = self._get_redis_key(redis_key)
        proxies = await self._client.zrangebyscore(key, proxy_score_min, proxy_score_max)
        result = convert_proxy_or_proxies(proxies)
        return result or []

    @ensure_connection
    async def count(self, redis_key=None) -> int:
        """
        异步获取代理数量
        :param redis_key: Redis键，None使用默认键
        :return: 代理数量
        """
        key = self._get_redis_key(redis_key)
        return await self._client.zcard(key)
    
    @ensure_connection
    async def decrease(self, proxy: Proxy, redis_key=REDIS_KEY, proxy_score_min=PROXY_SCORE_MIN) -> int:
        """
        异步减少代理分数
        :param proxy: 代理对象
        :param redis_key: Redis键
        :param proxy_score_min: 最小分数
        :return: 新分数
        """
        proxy_str = proxy.string()
        await self._client.zincrby(redis_key, -1, proxy_str)
        score = await self._client.zscore(redis_key, proxy_str)
        logger.info(f'{proxy_str} score decrease 1, current {score}')
        
        if score <= proxy_score_min:
            logger.info(f'{proxy_str} current score {score}, remove')
            await self._client.zrem(redis_key, proxy_str)
            return 0
        return int(score)
    
    @ensure_connection
    async def max(self, proxy: Proxy, redis_key=REDIS_KEY, proxy_score_max=PROXY_SCORE_MAX) -> int:
        """
        异步设置代理为最高分
        :param proxy: 代理对象
        :param redis_key: Redis键
        :param proxy_score_max: 最高分数
        :return: 操作结果
        """
        logger.info(f'{proxy.string()} is valid, set to {proxy_score_max}')
        return await self._client.zadd(redis_key, {proxy.string(): proxy_score_max})
        
    @ensure_connection
    async def exists(self, proxy: Proxy, redis_key=REDIS_KEY) -> bool:
        """
        异步检查代理是否存在
        :param proxy: 代理对象
        :param redis_key: Redis键
        :return: 是否存在
        """
        score = await self._client.zscore(redis_key, proxy.string())
        return score is not None
    
    @ensure_connection
    async def zremrangebyscore(self, redis_key=REDIS_KEY, min_score=0, max_score=100) -> int:
        """
        异步删除指定分数范围内的代理
        :param redis_key: Redis键
        :param min_score: 最小分数
        :param max_score: 最大分数
        :return: 删除的数量
        """
        return await self._client.zremrangebyscore(redis_key, min_score, max_score)
    
    @ensure_connection
    async def zcount(self, redis_key=REDIS_KEY, min_score=0, max_score=100) -> int:
        """
        异步获取指定分数范围内的代理数量
        :param redis_key: Redis键
        :param min_score: 最小分数
        :param max_score: 最大分数
        :return: 代理数量
        """
        return await self._client.zcount(redis_key, min_score, max_score)
    
    @ensure_connection
    async def batch(self, cursor, count, redis_key=REDIS_KEY):
        """
        异步批量获取代理
        :param cursor: scan cursor
        :param count: scan count
        :param redis_key: Redis键
        :return: (cursor, list of proxies)
        """
        cursor, proxies = await self._client.zscan(redis_key, cursor, count=count)
        return cursor, convert_proxy_or_proxies([i[0] for i in proxies])
    
    async def close(self):
        """关闭连接"""
        if self._client:
            await self._client.close()
            self._client = None
