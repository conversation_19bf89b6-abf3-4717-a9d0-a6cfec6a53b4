# 基于协议的代理存储设计文档

## 概述

本设计文档描述了如何重构现有代理池系统，实现基于协议类型的分表存储。系统将根据代理协议（HTTP、SOCKS4、SOCKS5）将代理分别存储到不同的Redis表中，同时保持API的向后兼容性。

## 架构设计

### 核心组件架构

```mermaid
graph TB
    A[代理爬虫] --> B[协议检测器]
    B --> C{协议类型}
    C -->|HTTP| D[HTTP代理存储]
    C -->|SOCKS4| E[SOCKS4代理存储]  
    C -->|SOCKS5| F[SOCKS5代理存储]
    
    D --> G[Redis: proxies:http]
    E --> H[Redis: proxies:socks4]
    F --> I[Redis: proxies:socks5]
    
    J[API层] --> K[协议路由器]
    K --> G
    K --> H
    K --> I
    
    L[测试器] --> K
    M[统计服务] --> K
```

### Redis表结构设计

#### 表命名规范
- HTTP代理表: `proxies:http`
- SOCKS4代理表: `proxies:socks4`  
- SOCKS5代理表: `proxies:socks5`
- 配置表: `proxies:config` (存储元数据)

#### 数据格式
```
Redis Key: proxies:http
Data Type: Sorted Set (ZSET)
Format: {
  "192.168.1.1:8080": 100,
  "user:pass@192.168.1.2:8080": 95,
  "192.168.1.3:3128": 80
}
```

## 组件设计

### 1. 协议检测器 (ProtocolDetector)

```python
class ProtocolDetector:
    """检测代理协议类型"""
    
    @staticmethod
    def detect_protocol(proxy_string: str, source_url_type: str = None) -> str:
        """
        检测代理协议类型
        :param proxy_string: 代理字符串
        :param source_url_type: 来源URL类型 ('http_urls', 'socks4_urls', 'socks5_urls')
        :return: 协议类型 ('http', 'socks4', 'socks5') 或 None（无法确定）
        """
        
    @staticmethod
    def normalize_proxy(proxy_string: str) -> tuple:
        """
        标准化代理格式，移除协议前缀
        :param proxy_string: 原始代理字符串
        :return: (normalized_proxy, protocol) 或 (None, None)（无法确定协议）
        """
        
    @staticmethod
    def infer_protocol_from_source(source_url_type: str) -> str:
        """
        根据来源URL类型推断协议
        :param source_url_type: URL类型标识
        :return: 推断的协议类型
        """
```

### 2. 协议感知存储客户端 (ProtocolAwareRedisClient)

```python
class ProtocolAwareRedisClient(AsyncRedisClient):
    """协议感知的Redis客户端"""
    
    def __init__(self):
        super().__init__()
        self.protocol_keys = {
            'http': 'proxies:http',
            'socks4': 'proxies:socks4', 
            'socks5': 'proxies:socks5'
        }
    
    async def add_by_protocol(self, proxy: Proxy, protocol: str, score: int):
        """根据协议类型添加代理"""
        
    async def get_by_protocol(self, protocol: str) -> Proxy:
        """根据协议类型获取代理"""
        
    async def get_random_any_protocol(self) -> Proxy:
        """从所有协议表中随机获取代理"""
        
    async def count_by_protocol(self, protocol: str) -> int:
        """获取指定协议的代理数量"""
        
    async def get_protocol_stats(self) -> dict:
        """获取所有协议的统计信息"""
```

### 3. 增强的Proxy模型

```python
@attrs(eq=False, hash=False)
class Proxy(object):
    """增强的代理模型"""
    host = attr(type=str, default=None)
    port = attr(type=int, default=None)
    protocol = attr(type=str, default='http')
    username = attr(type=str, default=None)
    password = attr(type=str, default=None)
    
    def get_redis_key(self) -> str:
        """获取对应的Redis键"""
        return f'proxies:{self.protocol}'
    
    def get_normalized_string(self) -> str:
        """获取标准化的代理字符串（无协议前缀）"""
        if self.username and self.password:
            return f'{self.username}:{self.password}@{self.host}:{self.port}'
        return f'{self.host}:{self.port}'
```

### 4. 协议路由器 (ProtocolRouter)

```python
class ProtocolRouter:
    """协议路由器，处理不同协议的请求路由"""
    
    def __init__(self, redis_client: ProtocolAwareRedisClient):
        self.redis_client = redis_client
    
    async def route_add_request(self, proxy: Proxy):
        """路由添加请求到对应协议表"""
        
    async def route_get_request(self, protocol: str = None) -> Proxy:
        """路由获取请求到对应协议表"""
        
    async def route_test_request(self, proxy: Proxy):
        """路由测试请求，使用协议特定的测试方法"""
```

### 5. 协议感知测试器 (ProtocolAwareTester)

```python
class ProtocolAwareTester:
    """协议感知的代理测试器"""
    
    def __init__(self):
        self.test_url = TEST_URL  # 统一的测试URL
    
    async def test_proxy(self, proxy: Proxy) -> bool:
        """
        根据协议类型测试代理
        :param proxy: 代理对象（包含协议信息）
        :return: 测试结果
        """
        
    async def _test_http_proxy(self, proxy: Proxy) -> bool:
        """测试HTTP代理"""
        
    async def _test_socks4_proxy(self, proxy: Proxy) -> bool:
        """测试SOCKS4代理"""
        
    async def _test_socks5_proxy(self, proxy: Proxy) -> bool:
        """测试SOCKS5代理"""
```

## 数据流设计

### 代理入库流程

```mermaid
sequenceDiagram
    participant C as Crawler
    participant PD as ProtocolDetector  
    participant PR as ProtocolRouter
    participant RC as RedisClient
    participant R as Redis
    
    C->>PD: 原始代理字符串
    PD->>PD: 检测协议类型
    PD->>PD: 标准化代理格式
    PD->>PR: (normalized_proxy, protocol)
    PR->>RC: add_by_protocol(proxy, protocol)
    RC->>R: ZADD proxies:{protocol}
    R-->>RC: 添加结果
    RC-->>PR: 操作结果
    PR-->>C: 入库完成
```

### 代理获取流程

```mermaid
sequenceDiagram
    participant API as API层
    participant PR as ProtocolRouter
    participant RC as RedisClient
    participant R as Redis
    
    API->>PR: 请求代理(protocol=http)
    PR->>RC: get_by_protocol('http')
    RC->>R: ZREVRANGEBYSCORE proxies:http
    R-->>RC: 代理列表
    RC->>RC: 转换为Proxy对象
    RC-->>PR: Proxy对象
    PR-->>API: 返回代理
```

## 接口设计

### REST API接口

#### 获取代理接口
```
GET /proxy?protocol=http
GET /proxy?protocol=socks4  
GET /proxy?protocol=socks5
GET /proxy  # 随机协议
```

#### 统计接口
```
GET /stats
Response: {
  "http": {"count": 150, "avg_score": 85},
  "socks4": {"count": 50, "avg_score": 78},
  "socks5": {"count": 100, "avg_score": 92},
  "total": 300
}
```

### 配置接口

#### 环境变量配置
```bash
# Redis键名配置
REDIS_KEY_HTTP=proxies:http
REDIS_KEY_SOCKS4=proxies:socks4  
REDIS_KEY_SOCKS5=proxies:socks5

# 默认协议配置
DEFAULT_PROTOCOL=http

# 协议优先级配置
PROTOCOL_PRIORITY=socks5,http,socks4
```

## 协议推断逻辑

### 协议检测优先级
1. **显式协议前缀**: `http://`, `socks4://`, `socks5://`
2. **来源URL类型推断**: 
   - `http_urls` → HTTP协议
   - `socks4_urls` → SOCKS4协议  
   - `socks5_urls` → SOCKS5协议
3. **无法确定**: 拒绝入库，记录警告日志

### 测试器协议处理

#### 统一测试URL
- 所有协议使用相同的测试URL（如 `http://www.baidu.com`）
- 区别在于连接方式，不是测试目标

#### 协议特定连接方式
```python
# HTTP代理测试
async with session.get(TEST_URL, proxy=f'http://{proxy.string()}') as response:

# SOCKS4代理测试  
async with session.get(TEST_URL, proxy=f'socks4://{proxy.string()}') as response:

# SOCKS5代理测试
async with session.get(TEST_URL, proxy=f'socks5://{proxy.string()}') as response:
```

## 错误处理

### 错误类型定义
```python
class ProtocolNotSupportedException(Exception):
    """不支持的协议类型异常"""
    
class ProtocolPoolEmptyException(Exception):
    """指定协议代理池为空异常"""
    
class ProtocolDetectionException(Exception):
    """协议检测失败异常"""
    
class ProtocolAmbiguousException(Exception):
    """协议不明确异常"""
```

### 错误处理策略
1. **协议检测失败**: 拒绝入库，记录警告日志
2. **指定协议池为空**: 尝试从其他协议池获取
3. **Redis连接失败**: 使用重试机制和降级策略
4. **协议不明确**: 拒绝处理，要求明确协议类型

## 测试策略

### 单元测试
- ProtocolDetector协议检测准确性测试
- ProtocolAwareRedisClient存储和获取功能测试
- ProtocolRouter路由逻辑测试

### 集成测试  
- 端到端代理入库和获取流程测试
- 多协议并发操作测试
- API接口功能测试

### 性能测试
- 大量代理入库性能测试
- 高并发代理获取性能测试
- Redis分表查询性能对比测试

## 迁移策略

### 数据迁移方案
1. **渐进式迁移**: 新代理使用新表结构，旧代理逐步迁移
2. **双写策略**: 过渡期间同时写入新旧表结构
3. **数据验证**: 迁移后验证数据完整性和一致性

### 兼容性保证
1. **API兼容**: 保持现有API接口不变
2. **配置兼容**: 支持旧配置格式的自动转换
3. **功能兼容**: 确保所有现有功能正常工作

## 监控和运维

### 监控指标
- 各协议代理数量和分数分布
- 代理入库和获取的成功率
- 不同协议的测试通过率
- Redis各表的性能指标

### 运维工具
- 协议分布统计脚本
- 数据迁移和验证工具
- 性能监控和告警配置
- 故障恢复和数据修复工具