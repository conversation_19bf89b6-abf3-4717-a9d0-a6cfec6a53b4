# ============================================================================
# HTTP Proxy Pool 环境变量配置文件模板
# ============================================================================

# ============================================================================
# 🔧 Redis数据库配置 (必需)
# ============================================================================
PROXYPOOL_REDIS_HOST=***************
PROXYPOOL_REDIS_PORT=16379
PROXYPOOL_REDIS_PASSWORD=Yyh275822746Xyz
PROXYPOOL_REDIS_DB=0
PROXYPOOL_REDIS_KEY=proxies:universal

# 备用Redis配置（如果不使用PROXYPOOL_前缀）
# REDIS_HOST=127.0.0.1
# REDIS_PORT=6379
# REDIS_PASSWORD=
# REDIS_DB=0
# REDIS_KEY=proxies:universal

# Redis连接字符串（可选，优先级最高）
# PROXYPOOL_REDIS_CONNECTION_STRING=redis://password@host:port/db
# REDIS_CONNECTION_STRING=redis://password@host:port/db

# ============================================================================
# 🌐 API服务配置
# ============================================================================
API_HOST=0.0.0.0
API_PORT=5555
API_KEY=yqproxy
# API_THREADED=true

# ============================================================================
# ⚙️ 核心服务启用开关
# ============================================================================
ENABLE_GETTER=true
ENABLE_TESTER=true
ENABLE_SERVER=true

# ============================================================================
# ⏰ 运行周期配置（秒）
# ============================================================================
CYCLE_GETTER=300
CYCLE_TESTER=60
# GET_TIMEOUT=10

# ============================================================================
# 🧪 代理测试配置
# ============================================================================
TEST_URL=https://push2his.eastmoney.com/api/qt/stock/kline/get?fields1=f1,f2,f3,f4,f5,f6&fields2=f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f116&ut=7eea3edcaed734bea9cbfc24409ed989&klt=101&fqt=0&secid=1.000001&beg=20250102&end=20250103
# TEST_TIMEOUT=10
# TEST_BATCH=20
TEST_ANONYMOUS=false
# TEST_VALID_STATUS=200,206,302
# TEST_DONT_SET_MAX_SCORE=false

# ============================================================================
# 📊 代理分数配置
# ============================================================================
PROXY_SCORE_MAX=100
PROXY_SCORE_MIN=0
PROXY_SCORE_INIT=10

# ============================================================================
# 智能代理池管理配置
# ============================================================================
# PROXY_NUMBER_MAX_INVALID=50000      # 无效代理池数量上限
# PROXY_NUMBER_MIN_VALID=100      # 最少有效代理数量（触发智能清理的阈值）

# 智能清理机制说明：
# 当总代理数达到上限且有效代理数不足时，自动清理分数在[11,70]范围内的动态代理
# 保留所有静态代理（域名代理）和高分代理（100分）

# ============================================================================
# �📝 日志配置
# ============================================================================
# LOG_DIR=logs
LOG_ROTATION=1 day
LOG_RETENTION=30 days
# LOG_RUNTIME_FILE=logs/runtime.log
# LOG_ERROR_FILE=logs/error.log

# 日志启用开关
# ENABLE_LOG_FILE=true
# ENABLE_LOG_RUNTIME_FILE=true
# ENABLE_LOG_ERROR_FILE=true

# ============================================================================
# 🚀 应用环境配置
# ============================================================================
# APP_ENV=prod
# APP_DEBUG=false
# APP_PROD_METHOD=gevent

# ============================================================================
# 📋 未使用的配置项（已注释）
# ============================================================================
# 以下配置项在setting.py中定义但当前未使用，如需要可取消注释

# 代理数量限制（现已支持环境变量）
# PROXY_NUMBER_MAX=50000
# PROXY_NUMBER_MIN=0

# 测试请求头（已注释的功能）
# TEST_HEADERS={"User-Agent": "Mozilla/5.0..."}

# 代理随机降级（配置项名称有误，实际读取TEST_ANONYMOUS）
# PROXY_RAND_KEY_DEGRADED=true
