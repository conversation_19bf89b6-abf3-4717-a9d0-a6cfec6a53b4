#!/bin/bash

# 协议感知代理池系统启动脚本

set -e

PROJECT_ROOT="D:\onedrive\proxy_pool\http_proxy_pool"
cd "$PROJECT_ROOT"

echo "🚀 启动协议感知代理池系统..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 检查Redis
if ! command -v redis-cli &> /dev/null; then
    echo "⚠️ Redis CLI 未安装，无法检查Redis状态"
else
    if ! redis-cli ping &> /dev/null; then
        echo "❌ Redis 未运行，请先启动Redis服务"
        exit 1
    fi
    echo "✅ Redis 连接正常"
fi

# 设置环境变量
export PYTHONPATH="$PROJECT_ROOT"

# 启动API服务器（后台）
echo "🌐 启动API服务器..."
nohup python3 -m http_proxy_pool.processors.protocol_aware_server > logs/api.log 2>&1 &
API_PID=$!
echo $API_PID > pids/api.pid
echo "✅ API服务器已启动 (PID: $API_PID)"

# 等待API服务器启动
sleep 5

# 启动Getter（后台）
echo "🕷️ 启动代理获取器..."
nohup python3 -c "
import asyncio
import time
from http_proxy_pool.processors.getter import Getter

async def run_getter():
    while True:
        try:
            print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}] 开始获取代理...')
            getter = Getter()
            await getter.run()
            await getter.redis.close()
            print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}] 代理获取完成，等待5分钟...')
            await asyncio.sleep(300)
        except Exception as e:
            print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}] Getter错误: {e}')
            await asyncio.sleep(60)

asyncio.run(run_getter())
" > logs/getter.log 2>&1 &
GETTER_PID=$!
echo $GETTER_PID > pids/getter.pid
echo "✅ 代理获取器已启动 (PID: $GETTER_PID)"

# 启动Tester（后台）
echo "🧪 启动代理测试器..."
nohup python3 -c "
import asyncio
import time
from http_proxy_pool.processors.tester import Tester

async def run_tester():
    while True:
        try:
            print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}] 开始测试代理...')
            tester = Tester()
            await tester.run()
            print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}] 代理测试完成，等待10分钟...')
            await asyncio.sleep(600)
        except Exception as e:
            print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}] Tester错误: {e}')
            await asyncio.sleep(120)

asyncio.run(run_tester())
" > logs/tester.log 2>&1 &
TESTER_PID=$!
echo $TESTER_PID > pids/tester.pid
echo "✅ 代理测试器已启动 (PID: $TESTER_PID)"

echo ""
echo "🎉 协议感知代理池系统启动完成！"
echo ""
echo "📊 服务状态:"
echo "  API服务器: http://localhost:5555 (PID: $API_PID)"
echo "  代理获取器: PID $GETTER_PID"
echo "  代理测试器: PID $TESTER_PID"
echo ""
echo "📋 管理命令:"
echo "  查看API日志: tail -f logs/api.log"
echo "  查看获取器日志: tail -f logs/getter.log"
echo "  查看测试器日志: tail -f logs/tester.log"
echo "  停止系统: ./stop.sh"
echo "  系统状态: ./status.sh"
echo ""
echo "🌐 API接口:"
echo "  健康检查: curl http://localhost:5555/health"
echo "  获取代理: curl http://localhost:5555/random"
echo "  协议统计: curl http://localhost:5555/stats"
