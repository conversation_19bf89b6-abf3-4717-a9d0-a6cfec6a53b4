"""
协议路由器
处理不同协议的请求路由和负载均衡
"""

from typing import Optional, List, Dict, Any
from loguru import logger

from http_proxy_pool.schemas.proxy import Proxy
from http_proxy_pool.storages.protocol_aware_redis_client import ProtocolAwareRedisClient
from http_proxy_pool.utils.protocol_detector import ProtocolType
from http_proxy_pool.exceptions import PoolEmptyException, ProtocolNotSupportedException


class ProtocolRouter:
    """
    协议路由器
    处理不同协议的请求路由、负载均衡和智能降级
    """
    
    def __init__(self, redis_client: ProtocolAwareRedisClient):
        """
        初始化协议路由器
        
        :param redis_client: 协议感知的Redis客户端
        """
        self.redis_client = redis_client
        
        # 协议优先级配置（可根据需要调整）
        self.protocol_priority = [
            ProtocolType.HTTP,
            ProtocolType.HTTPS,
            ProtocolType.SOCKS5,
            ProtocolType.SOCKS4
        ]
    
    async def route_add_request(self, proxy: Proxy) -> bool:
        """
        路由添加请求到对应协议表
        
        :param proxy: 代理对象
        :return: 是否添加成功
        """
        try:
            result = await self.redis_client.add_by_protocol(proxy)
            if result > 0:
                logger.debug(f'成功添加代理到 {proxy.get_redis_key()}: {proxy.get_normalized_string()}')
                return True
            else:
                logger.debug(f'代理已存在或添加失败: {proxy.get_normalized_string()}')
                return False
        except Exception as e:
            logger.error(f'添加代理失败: {proxy.get_normalized_string()} -> {e}')
            return False
    
    async def route_get_request(self, protocol: Optional[str] = None, 
                              preferred_protocols: Optional[List[str]] = None) -> Proxy:
        """
        路由获取请求到对应协议表（智能负载均衡）
        
        :param protocol: 指定协议类型
        :param preferred_protocols: 优先协议列表
        :return: 代理对象
        :raises PoolEmptyException: 所有协议池都为空时抛出
        :raises ProtocolNotSupportedException: 不支持的协议时抛出
        """
        if protocol:
            # 获取指定协议的代理
            try:
                protocol_enum = ProtocolType(protocol.lower())
                return await self.redis_client.get_by_protocol(protocol_enum)
            except ValueError:
                raise ProtocolNotSupportedException(protocol)
        
        # 智能获取代理（支持优先协议）
        if preferred_protocols:
            try:
                protocol_enums = [ProtocolType(p.lower()) for p in preferred_protocols]
                return await self.redis_client.get_random_any_protocol(protocol_enums)
            except ValueError as e:
                logger.warning(f'无效的优先协议: {e}，使用默认优先级')
        
        # 使用默认优先级获取代理
        return await self.redis_client.get_random_any_protocol(self.protocol_priority)
    
    async def route_batch_get_request(self, count: int, protocol: Optional[str] = None,
                                    preferred_protocols: Optional[List[str]] = None) -> List[Proxy]:
        """
        批量获取代理（高性能版本）
        
        :param count: 需要获取的代理数量
        :param protocol: 指定协议类型
        :param preferred_protocols: 优先协议列表
        :return: 代理列表
        """
        if count <= 0:
            return []
        
        proxies = []
        
        if protocol:
            # 从指定协议获取
            try:
                protocol_enum = ProtocolType(protocol.lower())
                all_proxies = await self.redis_client.get_all_by_protocol(protocol_enum)
                proxies = all_proxies[:count] if all_proxies else []
            except ValueError:
                raise ProtocolNotSupportedException(protocol)
        else:
            # 从多个协议智能获取
            protocols_to_try = preferred_protocols or [p.value for p in self.protocol_priority]
            
            for proto in protocols_to_try:
                if len(proxies) >= count:
                    break
                
                try:
                    protocol_enum = ProtocolType(proto.lower())
                    remaining_count = count - len(proxies)
                    protocol_proxies = await self.redis_client.get_all_by_protocol(protocol_enum)
                    
                    if protocol_proxies:
                        proxies.extend(protocol_proxies[:remaining_count])
                        
                except (ValueError, PoolEmptyException):
                    continue
        
        return proxies[:count]
    
    async def route_test_request(self, proxy: Proxy) -> bool:
        """
        路由测试请求，使用协议特定的测试方法
        
        :param proxy: 代理对象
        :return: 测试是否成功
        """
        from http_proxy_pool.testers.protocol_aware_tester import ProtocolAwareTester
        
        try:
            tester = ProtocolAwareTester()
            return await tester.test_proxy_by_protocol(proxy)
        except Exception as e:
            logger.error(f'代理测试失败: {proxy.get_normalized_string()} -> {e}')
            return False
    
    async def get_protocol_statistics(self) -> Dict[str, Any]:
        """
        获取协议统计信息
        
        :return: 统计信息字典
        """
        try:
            counts = await self.redis_client.count_by_protocol()
            stats = await self.redis_client.get_protocol_stats()
            
            result = {
                'total_proxies': sum(counts.values()),
                'protocols': {}
            }
            
            for protocol, count in counts.items():
                protocol_stats = stats.get(protocol, {})
                result['protocols'][protocol] = {
                    'count': count,
                    'high_score': protocol_stats.get('high_score', 0),
                    'medium_score': protocol_stats.get('medium_score', 0),
                    'low_score': protocol_stats.get('low_score', 0),
                    'health_ratio': round(protocol_stats.get('health_ratio', 0) * 100, 2)
                }
            
            return result
        except Exception as e:
            logger.error(f'获取协议统计失败: {e}')
            return {'error': str(e)}
    
    async def cleanup_invalid_proxies(self) -> Dict[str, int]:
        """
        清理无效代理
        
        :return: 清理结果统计
        """
        try:
            return await self.redis_client.cleanup_empty_protocols()
        except Exception as e:
            logger.error(f'清理无效代理失败: {e}')
            return {'error': str(e)}
    
    async def migrate_from_universal_table(self, universal_key: str = "proxies") -> Dict[str, int]:
        """
        从通用表迁移数据到协议分表
        
        :param universal_key: 通用表的Redis键
        :return: 迁移结果统计
        """
        try:
            return await self.redis_client.migrate_from_universal_table(universal_key)
        except Exception as e:
            logger.error(f'数据迁移失败: {e}')
            return {'error': str(e)}
    
    async def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        :return: 健康状态信息
        """
        try:
            counts = await self.redis_client.count_by_protocol()
            total_proxies = sum(counts.values())
            
            # 检查是否有可用代理
            has_proxies = total_proxies > 0
            
            # 检查各协议是否有高分代理
            stats = await self.redis_client.get_protocol_stats()
            healthy_protocols = []
            
            for protocol, stat in stats.items():
                if stat.get('high_score', 0) > 0:
                    healthy_protocols.append(protocol)
            
            return {
                'status': 'healthy' if has_proxies else 'warning',
                'total_proxies': total_proxies,
                'healthy_protocols': healthy_protocols,
                'protocol_distribution': counts,
                'timestamp': __import__('time').time()
            }
        except Exception as e:
            logger.error(f'健康检查失败: {e}')
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': __import__('time').time()
            }
