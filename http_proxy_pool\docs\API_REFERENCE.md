# 协议感知代理池API参考文档

## 概述

协议感知代理池API提供了完整的代理管理功能，支持HTTP、HTTPS、SOCKS4、SOCKS5四种协议的智能路由和管理。

## 基础信息

- **基础URL**: `http://localhost:5555`
- **API版本**: `3.0.0`
- **认证方式**: API Key（可选）
- **响应格式**: JSON / 纯文本

## 认证

如果配置了API_KEY，需要在请求中包含认证参数：

```bash
curl "http://localhost:5555/random?API-KEY=your_api_key"
```

## 核心接口

### 1. 获取随机代理

**接口**: `GET /random`

**描述**: 获取一个随机代理，支持协议过滤和优先级设置

**参数**:
- `protocol` (可选): 指定协议类型 (`http`, `https`, `socks4`, `socks5`)
- `preferred` (可选): 优先协议列表，逗号分隔 (如: `http,socks5`)

**响应**: 纯文本格式的代理地址

**示例**:
```bash
# 获取任意协议代理
curl "http://localhost:5555/random"
# 响应: ***********:8080

# 获取HTTP代理
curl "http://localhost:5555/random?protocol=http"
# 响应: ***********:8080

# 按优先级获取代理
curl "http://localhost:5555/random?preferred=socks5,http"
# 响应: ***********:1080
```

**错误响应**:
- `400`: 不支持的协议类型
- `404`: 没有可用的代理

### 2. 获取指定协议代理

**接口**: `GET /protocol/{protocol_name}`

**描述**: 获取指定协议类型的代理

**路径参数**:
- `protocol_name`: 协议类型 (`http`, `https`, `socks4`, `socks5`)

**响应**: 纯文本格式的代理地址

**示例**:
```bash
# 获取HTTP代理
curl "http://localhost:5555/protocol/http"
# 响应: ***********:8080

# 获取SOCKS5代理
curl "http://localhost:5555/protocol/socks5"
# 响应: ***********:1080
```

### 3. 批量获取代理

**接口**: `GET /batch`

**描述**: 批量获取多个代理

**参数**:
- `count`: 获取数量 (1-1000，默认10)
- `protocol` (可选): 指定协议类型
- `preferred` (可选): 优先协议列表，逗号分隔

**响应**: 每行一个代理地址的纯文本

**示例**:
```bash
# 批量获取10个代理
curl "http://localhost:5555/batch?count=10"

# 批量获取5个HTTP代理
curl "http://localhost:5555/batch?count=5&protocol=http"

# 按优先级批量获取
curl "http://localhost:5555/batch?count=3&preferred=socks5,http"
```

**响应示例**:
```
***********:8080
***********:8080
***********:8080
```

## 查询接口

### 4. 获取所有代理

**接口**: `GET /all/{protocol_name}`

**描述**: 获取指定协议的所有代理

**路径参数**:
- `protocol_name`: 协议类型

**响应**: 每行一个代理地址的纯文本

**示例**:
```bash
curl "http://localhost:5555/all/http"
```

### 5. 获取有效代理

**接口**: `GET /valid/{protocol_name}`

**描述**: 获取指定协议的所有有效代理（高分代理）

**路径参数**:
- `protocol_name`: 协议类型

**响应**: 每行一个代理地址的纯文本

**示例**:
```bash
curl "http://localhost:5555/valid/socks5"
```

### 6. 获取代理数量

**接口**: `GET /count`

**描述**: 获取各协议代理数量统计

**响应**: JSON格式的统计信息

**示例**:
```bash
curl "http://localhost:5555/count"
```

**响应示例**:
```json
{
  "total": 1250,
  "protocols": {
    "http": 500,
    "https": 300,
    "socks4": 200,
    "socks5": 250
  }
}
```

### 7. 获取指定协议数量

**接口**: `GET /count/{protocol_name}`

**描述**: 获取指定协议的代理数量

**路径参数**:
- `protocol_name`: 协议类型

**响应**: 纯文本格式的数量

**示例**:
```bash
curl "http://localhost:5555/count/http"
# 响应: 500
```

## 统计接口

### 8. 获取详细统计

**接口**: `GET /stats`

**描述**: 获取详细的协议统计信息

**响应**: JSON格式的详细统计

**示例**:
```bash
curl "http://localhost:5555/stats"
```

**响应示例**:
```json
{
  "total_proxies": 1250,
  "protocols": {
    "http": {
      "count": 500,
      "high_score": 50,
      "medium_score": 200,
      "low_score": 250,
      "health_ratio": 10.0
    },
    "https": {
      "count": 300,
      "high_score": 30,
      "medium_score": 120,
      "low_score": 150,
      "health_ratio": 10.0
    },
    "socks4": {
      "count": 200,
      "high_score": 20,
      "medium_score": 80,
      "low_score": 100,
      "health_ratio": 10.0
    },
    "socks5": {
      "count": 250,
      "high_score": 25,
      "medium_score": 100,
      "low_score": 125,
      "health_ratio": 10.0
    }
  }
}
```

### 9. 健康检查

**接口**: `GET /health`

**描述**: 系统健康检查

**响应**: JSON格式的健康状态

**示例**:
```bash
curl "http://localhost:5555/health"
```

**响应示例**:
```json
{
  "status": "healthy",
  "total_proxies": 1250,
  "healthy_protocols": ["http", "https", "socks5"],
  "protocol_distribution": {
    "http": 500,
    "https": 300,
    "socks4": 200,
    "socks5": 250
  },
  "timestamp": **********.0
}
```

**状态说明**:
- `healthy`: 系统正常，有可用代理
- `warning`: 系统运行但代理数量较少
- `error`: 系统异常

## 管理接口

### 10. 清理无效代理

**接口**: `POST /cleanup`

**描述**: 清理所有协议表中的无效代理

**响应**: JSON格式的清理结果

**示例**:
```bash
curl -X POST "http://localhost:5555/cleanup"
```

**响应示例**:
```json
{
  "message": "清理完成",
  "result": {
    "http": 50,
    "https": 30,
    "socks4": 20,
    "socks5": 25
  }
}
```

### 11. 数据迁移

**接口**: `POST /migrate`

**描述**: 从通用表迁移数据到协议分表

**参数**:
- `universal_key`: 通用表Redis键 (默认: "proxies")

**响应**: JSON格式的迁移结果

**示例**:
```bash
curl -X POST "http://localhost:5555/migrate?universal_key=old_proxies"
```

**响应示例**:
```json
{
  "message": "迁移完成",
  "result": {
    "http": 300,
    "https": 150,
    "socks4": 100,
    "socks5": 200,
    "failed": 10,
    "total": 760
  }
}
```

## 兼容性接口

### 12. 获取所有代理（兼容）

**接口**: `GET /all`

**描述**: 获取所有协议的代理（兼容旧版本）

**响应**: 每行一个代理地址的纯文本

**示例**:
```bash
curl "http://localhost:5555/all"
```

## 错误处理

### HTTP状态码

- `200`: 请求成功
- `206`: 部分成功（健康检查警告状态）
- `400`: 请求参数错误
- `403`: API密钥无效
- `404`: 资源不存在（如代理池为空）
- `500`: 内部服务器错误
- `503`: 服务不可用

### 错误响应格式

```json
{
  "detail": "错误描述信息"
}
```

### 常见错误

**不支持的协议**:
```json
{
  "detail": "不支持的协议: unknown_protocol"
}
```

**代理池为空**:
```json
{
  "detail": "没有可用的HTTP代理"
}
```

**API密钥错误**:
```json
{
  "detail": "提供的API密钥无效"
}
```

## 使用示例

### Python客户端

```python
import requests

class ProxyPoolClient:
    def __init__(self, base_url="http://localhost:5555", api_key=None):
        self.base_url = base_url
        self.api_key = api_key
    
    def _get_params(self, params=None):
        if params is None:
            params = {}
        if self.api_key:
            params['API-KEY'] = self.api_key
        return params
    
    def get_random_proxy(self, protocol=None, preferred=None):
        params = self._get_params()
        if protocol:
            params['protocol'] = protocol
        if preferred:
            params['preferred'] = preferred
        
        response = requests.get(f"{self.base_url}/random", params=params)
        return response.text.strip() if response.status_code == 200 else None
    
    def get_batch_proxies(self, count=10, protocol=None):
        params = self._get_params({'count': count})
        if protocol:
            params['protocol'] = protocol
        
        response = requests.get(f"{self.base_url}/batch", params=params)
        if response.status_code == 200:
            return response.text.strip().split('\n')
        return []
    
    def get_stats(self):
        params = self._get_params()
        response = requests.get(f"{self.base_url}/stats", params=params)
        return response.json() if response.status_code == 200 else None

# 使用示例
client = ProxyPoolClient(api_key="your_api_key")

# 获取随机HTTP代理
http_proxy = client.get_random_proxy(protocol="http")
print(f"HTTP代理: {http_proxy}")

# 批量获取SOCKS5代理
socks5_proxies = client.get_batch_proxies(count=5, protocol="socks5")
print(f"SOCKS5代理: {socks5_proxies}")

# 获取统计信息
stats = client.get_stats()
print(f"总代理数: {stats['total_proxies']}")
```

### JavaScript客户端

```javascript
class ProxyPoolClient {
    constructor(baseUrl = 'http://localhost:5555', apiKey = null) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
    }
    
    async getRandomProxy(protocol = null, preferred = null) {
        const params = new URLSearchParams();
        if (this.apiKey) params.append('API-KEY', this.apiKey);
        if (protocol) params.append('protocol', protocol);
        if (preferred) params.append('preferred', preferred);
        
        const response = await fetch(`${this.baseUrl}/random?${params}`);
        return response.ok ? await response.text() : null;
    }
    
    async getBatchProxies(count = 10, protocol = null) {
        const params = new URLSearchParams({ count });
        if (this.apiKey) params.append('API-KEY', this.apiKey);
        if (protocol) params.append('protocol', protocol);
        
        const response = await fetch(`${this.baseUrl}/batch?${params}`);
        if (response.ok) {
            const text = await response.text();
            return text.trim().split('\n');
        }
        return [];
    }
    
    async getStats() {
        const params = new URLSearchParams();
        if (this.apiKey) params.append('API-KEY', this.apiKey);
        
        const response = await fetch(`${this.baseUrl}/stats?${params}`);
        return response.ok ? await response.json() : null;
    }
}

// 使用示例
const client = new ProxyPoolClient('http://localhost:5555', 'your_api_key');

// 获取随机代理
client.getRandomProxy('http').then(proxy => {
    console.log('HTTP代理:', proxy);
});

// 获取统计信息
client.getStats().then(stats => {
    console.log('总代理数:', stats.total_proxies);
});
```

## 性能建议

1. **批量获取**: 需要多个代理时使用`/batch`接口而不是多次调用`/random`
2. **协议指定**: 明确指定协议类型可以提高响应速度
3. **缓存结果**: 客户端可以适当缓存代理列表减少API调用
4. **连接复用**: 使用HTTP连接池复用连接
5. **错误处理**: 实现适当的重试和降级机制

## 监控建议

1. **健康检查**: 定期调用`/health`接口监控系统状态
2. **统计监控**: 监控各协议的代理数量和健康度
3. **错误率**: 监控API接口的错误率和响应时间
4. **告警设置**: 设置代理数量过低或健康度过低的告警

## 更新日志

### v3.0.0
- 新增协议感知功能
- 支持HTTP、HTTPS、SOCKS4、SOCKS5四种协议
- 新增批量获取接口
- 新增详细统计接口
- 新增健康检查接口
- 优化性能和错误处理