# 需求文档

## 介绍

改进基类爬虫的HTTP 429状态码（频率限制）处理机制。当前基类的重试间隔固定为2秒，对于频率限制可能不够，需要在基类中直接处理429状态码，使用更合适的延迟策略，提高爬虫的稳定性和成功率。

## 需求

### 需求1

**用户故事：** 作为一个爬虫开发者，我希望基类能够智能处理429状态码，以便在遇到频率限制时能够自动调整重试策略。

#### 验收标准

1. 当收到429状态码时，系统应该使用更长的延迟时间进行重试
2. 如果响应头中包含Retry-After信息，系统应该遵循该延迟时间
3. 如果没有Retry-After信息，系统应该使用递增的延迟策略（如5秒、10秒、20秒）
4. 系统应该记录429错误的详细信息用于调试

### 需求2

**用户故事：** 作为一个爬虫开发者，我希望429处理使用合理的默认参数，无需额外配置即可正常工作。

#### 验收标准

1. 系统应该使用合理的默认429重试次数（如3次）
2. 系统应该使用合理的默认延迟策略（如5秒、10秒、20秒递增）
3. 系统应该有合理的最大延迟上限（如30秒）
4. 所有参数都应该硬编码在代码中，不需要配置文件

### 需求3

**用户故事：** 作为一个爬虫用户，我希望系统能够区分不同类型的HTTP错误，以便对429错误采用特殊的处理策略而不影响其他错误的处理。

#### 验收标准

1. 系统应该区分429错误和其他HTTP错误
2. 对于429错误，系统应该使用专门的重试逻辑
3. 对于其他错误，系统应该保持原有的处理方式
4. 系统应该在日志中清楚标识429错误的处理过程

### 需求4

**用户故事：** 作为一个开发者，我希望改进后的基类保持向后兼容性，以便现有的爬虫代码无需修改即可使用新功能。

#### 验收标准

1. 现有的fetch方法签名应该保持不变
2. 现有的爬虫子类应该无需修改即可使用新功能
3. 新功能应该是可选的，不影响现有功能
4. 系统应该提供平滑的升级路径