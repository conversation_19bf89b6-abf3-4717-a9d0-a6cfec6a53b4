[Unit]
Description=Protocol Aware Proxy Pool System
After=network.target redis.service
Requires=redis.service

[Service]
Type=simple
User=proxy-pool
Group=proxy-pool
WorkingDirectory=D:\onedrive\proxy_pool\http_proxy_pool
Environment=PYTHONPATH=D:\onedrive\proxy_pool\http_proxy_pool

# API服务器
ExecStart=/usr/bin/python3 -m http_proxy_pool.processors.protocol_aware_server
Restart=always
RestartSec=10

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=proxy-pool-api

[Install]
WantedBy=multi-user.target
