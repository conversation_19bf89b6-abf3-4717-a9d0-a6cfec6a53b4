from enum import IntEnum


class ErrCode(IntEnum):
    # 通用错误
    COMMON_ERROR = 1
    UNKNOWN_ERROR = 2

    # ============ 配置模块错误 ============
    _CONFIG_ERR_BEGIN = 100
    CONFIG_FILE_NOT_FOUND = 101          # 配置文件未找到
    CONFIG_PARSE_ERROR = 102             # 配置文件解析错误
    CONFIG_VALIDATION_ERROR = 103        # 配置验证失败
    CONFIG_SAVE_ERROR = 104              # 配置保存失败
    CONFIG_RELOAD_ERROR = 105            # 配置重载失败
    CONFIG_INVALID_VALUE = 106           # 配置值无效
    CONFIG_MISSING_REQUIRED = 107        # 缺少必需配置项
    _CONFIG_ERR_END = 199

    # ============ 代理获取器错误 ============
    _GETTER_ERR_BEGIN = 200
    GETTER_CRAWLER_INIT_ERROR = 201      # 爬虫初始化失败
    GETTER_FETCH_ERROR = 202             # 获取代理失败
    GETTER_PARSE_ERROR = 203             # 解析代理失败
    GETTER_NETWORK_ERROR = 204           # 网络连接错误
    GETTER_TIMEOUT_ERROR = 205           # 获取超时
    GETTER_RETRY_EXCEEDED = 206          # 重试次数超限
    GETTER_INVALID_SOURCE = 207          # 无效的代理源
    GETTER_POOL_FULL = 208               # 代理池已满
    _GETTER_ERR_END = 299

    # ============ 代理测试器错误 ============
    _TESTER_ERR_BEGIN = 300
    TESTER_INIT_ERROR = 301              # 测试器初始化失败
    TESTER_PROXY_INVALID = 302           # 代理无效
    TESTER_CONNECTION_ERROR = 303        # 连接错误
    TESTER_TIMEOUT_ERROR = 304           # 测试超时
    TESTER_HTTP_ERROR = 305              # HTTP错误
    TESTER_RESPONSE_ERROR = 306          # 响应错误
    TESTER_BATCH_ERROR = 307             # 批处理错误
    TESTER_ASYNC_ERROR = 308             # 异步处理错误
    _TESTER_ERR_END = 399

    # ============ API服务器错误 ============
    _SERVER_ERR_BEGIN = 400
    SERVER_START_ERROR = 401             # 服务器启动失败
    SERVER_AUTH_ERROR = 402              # 认证失败
    SERVER_REQUEST_ERROR = 403           # 请求处理错误
    SERVER_RESPONSE_ERROR = 404          # 响应错误
    SERVER_INTERNAL_ERROR = 405          # 服务器内部错误
    SERVER_PORT_OCCUPIED = 406           # 端口被占用
    SERVER_SHUTDOWN_ERROR = 407          # 服务器关闭错误
    _SERVER_ERR_END = 499

    # ============ Web界面错误 ============
    _WEB_ERR_BEGIN = 500
    WEB_TEMPLATE_ERROR = 501             # 模板错误
    WEB_STATIC_FILE_ERROR = 502          # 静态文件错误
    WEB_RENDER_ERROR = 503               # 渲染错误
    WEB_CONFIG_MANAGE_ERROR = 504        # 配置管理错误
    WEB_UI_ERROR = 505                   # UI错误
    WEB_ASSET_ERROR = 506                # 资源错误
    _WEB_ERR_END = 599

    # ============ Redis存储错误 ============
    _REDIS_ERR_BEGIN = 600
    REDIS_CONNECTION_ERROR = 601         # Redis连接错误
    REDIS_AUTH_ERROR = 602               # Redis认证失败
    REDIS_OPERATION_ERROR = 603          # Redis操作错误
    REDIS_POOL_EMPTY = 604               # 代理池为空 (PoolEmptyException归这里)
    REDIS_KEY_ERROR = 605                # Redis键错误
    REDIS_SCORE_ERROR = 606              # 分数操作错误
    REDIS_TIMEOUT_ERROR = 607            # Redis超时
    REDIS_VERSION_ERROR = 608            # Redis版本不兼容
    _REDIS_ERR_END = 699

    # ============ 协议处理错误 ============
    _PROTOCOL_ERR_BEGIN = 700
    PROTOCOL_DETECTION_ERROR = 701       # 协议检测失败
    PROTOCOL_NOT_SUPPORTED = 702         # 不支持的协议
    PROTOCOL_VALIDATION_ERROR = 703      # 协议验证失败
    PROTOCOL_MISMATCH_ERROR = 704        # 协议不匹配
    PROTOCOL_INFERENCE_ERROR = 705       # 协议推断失败
    PROTOCOL_PARSE_ERROR = 706           # 协议解析错误
    PROTOCOL_STANDARDIZE_ERROR = 707     # 协议标准化错误
    _PROTOCOL_ERR_END = 799


class ProxyPoolException(Exception):
    """HTTP代理池通用异常类"""

    def __init__(self, message, code=ErrCode.COMMON_ERROR):
        self.errcode = code
        self.msg = message
        Exception.__init__(self, message)

    def is_config_err(self):
        """判断是否为配置模块错误"""
        return ErrCode._CONFIG_ERR_BEGIN < self.errcode < ErrCode._CONFIG_ERR_END

    def is_getter_err(self):
        """判断是否为代理获取器错误"""
        return ErrCode._GETTER_ERR_BEGIN < self.errcode < ErrCode._GETTER_ERR_END

    def is_tester_err(self):
        """判断是否为代理测试器错误"""
        return ErrCode._TESTER_ERR_BEGIN < self.errcode < ErrCode._TESTER_ERR_END

    def is_server_err(self):
        """判断是否为API服务器错误"""
        return ErrCode._SERVER_ERR_BEGIN < self.errcode < ErrCode._SERVER_ERR_END

    def is_web_err(self):
        """判断是否为Web界面错误"""
        return ErrCode._WEB_ERR_BEGIN < self.errcode < ErrCode._WEB_ERR_END

    def is_redis_err(self):
        """判断是否为Redis存储错误"""
        return ErrCode._REDIS_ERR_BEGIN < self.errcode < ErrCode._REDIS_ERR_END

    def is_protocol_err(self):
        """判断是否为协议处理错误"""
        return ErrCode._PROTOCOL_ERR_BEGIN < self.errcode < ErrCode._PROTOCOL_ERR_END

    def get_error_category(self):
        """获取错误类别名称"""
        if self.is_config_err():
            return "CONFIG"
        elif self.is_getter_err():
            return "GETTER"
        elif self.is_tester_err():
            return "TESTER"
        elif self.is_server_err():
            return "SERVER"
        elif self.is_web_err():
            return "WEB"
        elif self.is_redis_err():
            return "REDIS"
        elif self.is_protocol_err():
            return "PROTOCOL"
        else:
            return "UNKNOWN"


class PoolEmptyException(ProxyPoolException):
    """代理池为空异常"""

    def __init__(self, message="no proxy in proxypool", redis_key=None):
        """
        初始化代理池为空异常
        :param message: 错误消息
        :param redis_key: 相关的Redis键名
        """
        if redis_key:
            message = f"no proxy in proxypool (key: {redis_key})"
        super().__init__(message, ErrCode.REDIS_POOL_EMPTY)
        self.redis_key = redis_key

    def __str__(self):
        """
        proxypool is used out
        :return:
        """
        return repr(self.msg)


# ============ 配置模块专用异常 ============
class ConfigException(ProxyPoolException):
    """配置模块异常基类"""
    pass


class ConfigFileNotFoundException(ConfigException):
    """配置文件未找到异常"""
    def __init__(self, config_path):
        super().__init__(f"配置文件未找到: {config_path}", ErrCode.CONFIG_FILE_NOT_FOUND)
        self.config_path = config_path


class ConfigParseException(ConfigException):
    """配置文件解析异常"""
    def __init__(self, config_path, parse_error):
        super().__init__(f"配置文件解析失败: {config_path} - {parse_error}", ErrCode.CONFIG_PARSE_ERROR)
        self.config_path = config_path
        self.parse_error = parse_error


# ============ 代理获取器专用异常 ============
class GetterException(ProxyPoolException):
    """代理获取器异常基类"""
    pass


class GetterNetworkException(GetterException):
    """代理获取器网络异常"""
    def __init__(self, url, error):
        super().__init__(f"获取代理网络错误: {url} - {error}", ErrCode.GETTER_NETWORK_ERROR)
        self.url = url
        self.error = error


class GetterTimeoutException(GetterException):
    """代理获取器超时异常"""
    def __init__(self, url, timeout):
        super().__init__(f"获取代理超时: {url} (超时时间: {timeout}s)", ErrCode.GETTER_TIMEOUT_ERROR)
        self.url = url
        self.timeout = timeout


# ============ 代理测试器专用异常 ============
class TesterException(ProxyPoolException):
    """代理测试器异常基类"""
    pass


class TesterConnectionException(TesterException):
    """代理测试器连接异常"""
    def __init__(self, proxy, error):
        super().__init__(f"代理连接失败: {proxy} - {error}", ErrCode.TESTER_CONNECTION_ERROR)
        self.proxy = proxy
        self.error = error


class TesterTimeoutException(TesterException):
    """代理测试器超时异常"""
    def __init__(self, proxy, timeout):
        super().__init__(f"代理测试超时: {proxy} (超时时间: {timeout}s)", ErrCode.TESTER_TIMEOUT_ERROR)
        self.proxy = proxy
        self.timeout = timeout


# ============ Redis存储专用异常 ============
class RedisException(ProxyPoolException):
    """Redis存储异常基类"""
    pass


class RedisConnectionException(RedisException):
    """Redis连接异常"""
    def __init__(self, host, port, error):
        super().__init__(f"Redis连接失败: {host}:{port} - {error}", ErrCode.REDIS_CONNECTION_ERROR)
        self.host = host
        self.port = port
        self.error = error


# ============ 协议处理专用异常 ============
class ProtocolException(ProxyPoolException):
    """协议处理异常基类"""
    pass


class ProtocolDetectionException(ProtocolException):
    """协议检测失败异常"""
    
    def __init__(self, message: str, proxy_string: str = None):
        super().__init__(message, ErrCode.PROTOCOL_DETECTION_ERROR)
        self.proxy_string = proxy_string
    
    def __str__(self):
        if self.proxy_string:
            return f"协议检测失败 '{self.proxy_string}': {self.msg}"
        return f"协议检测失败: {self.msg}"


class ProtocolNotSupportedException(ProtocolException):
    """不支持的协议异常"""
    
    def __init__(self, protocol: str):
        self.protocol = protocol
        super().__init__(f"不支持的协议 '{protocol}'", ErrCode.PROTOCOL_NOT_SUPPORTED)


class ProtocolValidationException(ProtocolException):
    """协议验证失败异常"""
    
    def __init__(self, message: str, proxy_string: str = None, expected_protocol: str = None):
        super().__init__(message, ErrCode.PROTOCOL_VALIDATION_ERROR)
        self.proxy_string = proxy_string
        self.expected_protocol = expected_protocol
    
    def __str__(self):
        base_msg = f"协议验证失败: {self.msg}"
        if self.proxy_string:
            base_msg += f" (代理: '{self.proxy_string}')"
        if self.expected_protocol:
            base_msg += f" (期望协议: {self.expected_protocol})"
        return base_msg


class ProtocolMismatchException(ProtocolException):
    """协议不匹配异常"""
    
    def __init__(self, detected_protocol: str, expected_protocol: str, proxy_string: str = None):
        self.detected_protocol = detected_protocol
        self.expected_protocol = expected_protocol
        self.proxy_string = proxy_string
        
        message = f"协议不匹配: 检测到 '{detected_protocol}', 期望 '{expected_protocol}'"
        if proxy_string:
            message += f" (代理: '{proxy_string}')"
        
        super().__init__(message, ErrCode.PROTOCOL_MISMATCH_ERROR)


class ProtocolInferenceException(ProtocolException):
    """协议推断失败异常"""
    
    def __init__(self, source_type: str):
        self.source_type = source_type
        super().__init__(f"无法从来源类型推断协议: '{source_type}'", ErrCode.PROTOCOL_INFERENCE_ERROR)


if __name__ == "__main__":
    # 测试错误分类系统
    def test_config_error():
        raise ProxyPoolException("配置文件未找到", ErrCode.CONFIG_FILE_NOT_FOUND)

    def test_redis_error():
        raise PoolEmptyException("代理池为空", "test_key")

    def test_protocol_error():
        raise ProtocolDetectionException("无法检测协议", "invalid:proxy:format")

    # 测试配置错误
    try:
        test_config_error()
    except ProxyPoolException as e:
        print(f"错误码: {e.errcode}")
        print(f"错误码名称: {e.errcode.name}")
        print(f"错误类别: {e.get_error_category()}")
        print(f"是否为配置错误: {e.is_config_err()}")
        print(f"错误消息: {e.msg}")
        print("---")

    # 测试Redis错误
    try:
        test_redis_error()
    except PoolEmptyException as e:
        print(f"错误码: {e.errcode}")
        print(f"错误码名称: {e.errcode.name}")
        print(f"错误类别: {e.get_error_category()}")
        print(f"是否为Redis错误: {e.is_redis_err()}")
        print(f"Redis键: {e.redis_key}")
        print(f"错误消息: {e.msg}")
        print("---")

    # 测试协议错误
    try:
        test_protocol_error()
    except ProtocolDetectionException as e:
        print(f"错误码: {e.errcode}")
        print(f"错误码名称: {e.errcode.name}")
        print(f"错误类别: {e.get_error_category()}")
        print(f"是否为协议错误: {e.is_protocol_err()}")
        print(f"代理字符串: {e.proxy_string}")
        print(f"错误消息: {e.msg}")
        print(f"字符串表示: {str(e)}")