# 协议感知代理池使用指南

## 概述

协议感知代理池是一个高性能的代理管理系统，支持HTTP、HTTPS、SOCKS4、SOCKS5四种协议的自动检测、分类存储和智能路由。

## 核心特性

### 🚀 高性能
- **协议检测**: 450万次/秒
- **代理创建**: 19万次/秒  
- **Redis操作**: 1万次/秒
- **并发测试**: 支持大规模并发代理测试

### 🎯 协议感知
- **自动检测**: 从代理字符串自动检测协议类型
- **智能推断**: 根据来源URL类型推断协议
- **分表存储**: 按协议自动分配到不同Redis键
- **专门测试**: 每种协议使用专门的测试方法

### 🛡️ 严格验证
- **拒绝无效**: 无法确定协议的代理被拒绝入库
- **数据质量**: 确保存储的代理都有明确的协议信息
- **错误处理**: 完善的异常处理和错误恢复机制

## 快速开始

### 1. 基本使用

```python
from http_proxy_pool.schemas.proxy import Proxy
from http_proxy_pool.storages.protocol_aware_redis_client import ProtocolAwareRedisClient

# 创建协议感知的代理对象
proxy = Proxy.from_string("http://192.168.1.1:8080")
print(f"协议: {proxy.protocol}")
print(f"Redis键: {proxy.get_redis_key()}")

# 使用协议感知Redis客户端
client = ProtocolAwareRedisClient()

# 按协议添加代理
await client.add_by_protocol(proxy)

# 按协议获取代理
http_proxy = await client.get_by_protocol(ProtocolType.HTTP)

# 获取协议统计
stats = await client.get_protocol_stats()
```

### 2. 爬虫配置

```python
from http_proxy_pool.crawlers.private.txt_proxies import TXTProxiesCrawler

# 配置不同协议的代理源
crawler = TXTProxiesCrawler(
    http_urls=[
        "https://example.com/http-proxies.txt"
    ],
    socks4_urls=[
        "https://example.com/socks4-proxies.txt"
    ],
    socks5_urls=[
        "https://example.com/socks5-proxies.txt"
    ]
)

# 爬虫会自动根据来源推断协议类型
for proxy in crawler.crawl():
    print(f"代理: {proxy}, 协议: {proxy.protocol}")
```

### 3. API服务器

```python
# 启动协议感知API服务器
from http_proxy_pool.processors.protocol_aware_server import app
import uvicorn

uvicorn.run(app, host="0.0.0.0", port=5555)
```

## API接口

### 基础接口

| 接口 | 方法 | 描述 | 示例 |
|------|------|------|------|
| `/random` | GET | 获取随机代理 | `/random?protocol=http` |
| `/protocol/{protocol}` | GET | 获取指定协议代理 | `/protocol/socks5` |
| `/batch` | GET | 批量获取代理 | `/batch?count=10&protocol=http` |
| `/stats` | GET | 获取协议统计 | `/stats` |
| `/health` | GET | 健康检查 | `/health` |

### 协议特定接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/all/{protocol}` | GET | 获取指定协议的所有代理 |
| `/valid/{protocol}` | GET | 获取指定协议的有效代理 |
| `/count/{protocol}` | GET | 获取指定协议的代理数量 |

### 管理接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/cleanup` | POST | 清理无效代理 |
| `/migrate` | POST | 数据迁移 |

## 配置说明

### 环境变量

```bash
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# API配置
API_HOST=0.0.0.0
API_PORT=5555
API_KEY=your_api_key

# 测试配置
TEST_URL=http://httpbin.org/ip
TEST_TIMEOUT=10
TEST_BATCH=100

# 协议感知配置
PROTOCOL_STRICT_MODE=true
PROTOCOL_DEFAULT_SCORE=10
```

### 代理源配置

在 `txt_proxies.py` 中配置不同协议的代理源：

```python
# HTTP代理源
HTTP_URLS = [
    'https://example.com/http-proxies.txt',
]

# SOCKS4代理源
SOCKS4_URLS = [
    'https://example.com/socks4-proxies.txt',
]

# SOCKS5代理源
SOCKS5_URLS = [
    'https://example.com/socks5-proxies.txt',
]
```

## 协议支持

### HTTP/HTTPS代理
- **格式**: `http://host:port`, `https://host:port`
- **认证**: `*********************:port`
- **测试方式**: 使用aiohttp的标准代理参数
- **Redis键**: `proxies:http`, `proxies:https`

### SOCKS4代理
- **格式**: `socks4://host:port`
- **测试方式**: 使用aiohttp-socks库
- **Redis键**: `proxies:socks4`

### SOCKS5代理
- **格式**: `socks5://host:port`, `socks5://user:pass@host:port`
- **认证支持**: 完整的用户名密码认证
- **测试方式**: 使用aiohttp-socks库
- **Redis键**: `proxies:socks5`

## 性能优化

### 1. 协议检测优化
- 使用LRU缓存缓存检测结果
- 预编译正则表达式
- 避免重复字符串操作

### 2. Redis操作优化
- 批量操作减少网络往返
- 连接池复用连接
- 异步并发操作

### 3. 测试优化
- 并发测试多个代理
- 协议特定的测试方法
- 智能超时和重试

## 监控和统计

### 协议分布统计
```python
client = ProtocolAwareRedisClient()
stats = await client.get_protocol_stats()

for protocol, stat in stats.items():
    print(f"{protocol.upper()}:")
    print(f"  总数: {stat['total']}")
    print(f"  高分: {stat['high_score']}")
    print(f"  健康度: {stat['health_ratio']:.2%}")
```

### 健康检查
```python
from http_proxy_pool.routers.protocol_router import ProtocolRouter

router = ProtocolRouter(client)
health = await router.health_check()

print(f"状态: {health['status']}")
print(f"总代理数: {health['total_proxies']}")
print(f"健康协议: {health['healthy_protocols']}")
```

## 错误处理

### 协议检测错误
```python
from http_proxy_pool.exceptions import ProtocolDetectionException

try:
    proxy = Proxy.from_string("invalid:format")
except ProtocolDetectionException as e:
    print(f"协议检测失败: {e}")
```

### 代理池为空
```python
from http_proxy_pool.exceptions import PoolEmptyException

try:
    proxy = await client.get_by_protocol(ProtocolType.HTTP)
except PoolEmptyException:
    print("HTTP代理池为空")
```

### 不支持的协议
```python
from http_proxy_pool.exceptions import ProtocolNotSupportedException

try:
    proxy = await router.route_get_request("unknown_protocol")
except ProtocolNotSupportedException as e:
    print(f"不支持的协议: {e.protocol}")
```

## 最佳实践

### 1. 代理源配置
- 按协议类型分别配置代理源URL
- 使用可靠的代理源，避免频繁失效
- 定期检查和更新代理源

### 2. 性能调优
- 根据实际需求调整批次大小
- 合理设置测试超时时间
- 使用连接池优化Redis连接

### 3. 监控告警
- 监控各协议的代理数量
- 设置健康度阈值告警
- 定期清理无效代理

### 4. 错误处理
- 实现优雅的降级策略
- 记录详细的错误日志
- 设置合理的重试机制

## 故障排除

### 常见问题

**Q: 协议检测失败怎么办？**
A: 检查代理字符串格式，确保包含协议前缀或配置正确的来源类型。

**Q: SOCKS代理测试失败？**
A: 确保已安装aiohttp-socks库：`pip install aiohttp-socks`

**Q: Redis连接失败？**
A: 检查Redis服务是否运行，配置是否正确。

**Q: API接口返回404？**
A: 检查指定的协议池是否有可用代理。

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
from loguru import logger
logger.add("debug.log", level="DEBUG")
```

## 升级指南

### 从传统代理池升级

1. **数据迁移**（如果需要）：
```python
router = ProtocolRouter(client)
result = await router.migrate_from_universal_table("proxies")
```

2. **更新爬虫配置**：
按协议类型重新配置代理源URL

3. **更新API调用**：
使用新的协议感知API接口

4. **测试验证**：
运行测试套件验证功能正常

## 贡献指南

欢迎贡献代码和建议！请遵循以下原则：

1. **性能优先**: 所有代码都要考虑性能影响
2. **严格验证**: 确保数据质量和系统稳定性
3. **完善测试**: 新功能必须包含测试用例
4. **清晰文档**: 更新相关文档和注释

## 许可证

本项目采用MIT许可证，详见LICENSE文件。