#!/usr/bin/env python3
"""
测试roosterkid链接的内容格式
"""
import sys
import os
sys.path.append('http_proxy_pool')

from http_proxy_pool.crawlers.base import BaseCrawler

class TestCrawler(BaseCrawler):
    def parse(self, html):
        return []

def test_roosterkid_urls():
    """测试roosterkid的三个URL"""
    crawler = TestCrawler()
    
    urls = [
        "https://raw.githubusercontent.com/roosterkid/openproxylist/main/HTTPS.txt",
        "https://raw.githubusercontent.com/roosterkid/openproxylist/main/SOCKS4.txt", 
        "https://raw.githubusercontent.com/roosterkid/openproxylist/main/SOCKS5.txt"
    ]
    
    for url in urls:
        print(f"\n=== 测试 {url} ===")
        result = crawler._fetch_with_429_handling(url, timeout=10)
        if result:
            lines = result.strip().split('\n')
            print(f"总行数: {len(lines)}")
            print("前10行内容:")
            for i, line in enumerate(lines[:10]):
                print(f"{i+1}: {line}")
            print("...")
            print("后5行内容:")
            for i, line in enumerate(lines[-5:]):
                print(f"{len(lines)-4+i}: {line}")
        else:
            print("获取失败")

if __name__ == "__main__":
    test_roosterkid_urls()