"""
错误处理策略和异常管理工具
统一的错误处理机制，确保系统稳定性
"""

import asyncio
from typing import Optional, Callable, Any, Dict
from functools import wraps
from loguru import logger

from http_proxy_pool.exceptions import (
    ProtocolDetectionException, ProtocolNotSupportedException,
    PoolEmptyException, RedisConnectionException, TesterException
)


class ErrorHandler:
    """
    统一错误处理器
    提供重试、降级、日志记录等功能
    """
    
    @staticmethod
    def with_retry(max_retries: int = 3, delay: float = 1.0, 
                   exceptions: tuple = (Exception,)):
        """
        重试装饰器
        
        :param max_retries: 最大重试次数
        :param delay: 重试延迟（秒）
        :param exceptions: 需要重试的异常类型
        """
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                last_exception = None
                
                for attempt in range(max_retries + 1):
                    try:
                        return await func(*args, **kwargs)
                    except exceptions as e:
                        last_exception = e
                        if attempt < max_retries:
                            logger.warning(f'函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}，{delay}秒后重试')
                            await asyncio.sleep(delay)
                        else:
                            logger.error(f'函数 {func.__name__} 重试 {max_retries} 次后仍然失败: {e}')
                
                raise last_exception
            return wrapper
        return decorator
    
    @staticmethod
    def with_fallback(fallback_func: Callable, 
                     exceptions: tuple = (Exception,)):
        """
        降级处理装饰器
        
        :param fallback_func: 降级处理函数
        :param exceptions: 需要降级的异常类型
        """
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    logger.warning(f'函数 {func.__name__} 失败，使用降级处理: {e}')
                    return await fallback_func(*args, **kwargs)
            return wrapper
        return decorator
    
    @staticmethod
    def log_errors(log_level: str = "error"):
        """
        错误日志装饰器
        
        :param log_level: 日志级别
        """
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    log_func = getattr(logger, log_level.lower())
                    log_func(f'函数 {func.__name__} 发生错误: {type(e).__name__}: {e}')
                    raise
            return wrapper
        return decorator


class ProtocolErrorHandler:
    """
    协议相关错误处理器
    专门处理协议检测、验证等错误
    """
    
    @staticmethod
    def handle_protocol_detection_error(proxy_string: str, error: Exception) -> Optional[str]:
        """
        处理协议检测错误
        
        :param proxy_string: 代理字符串
        :param error: 异常对象
        :return: 处理结果消息
        """
        if isinstance(error, ProtocolDetectionException):
            logger.warning(f'协议检测失败，拒绝代理: {proxy_string} -> {error}')
            return f"协议检测失败: {error}"
        
        elif isinstance(error, ProtocolNotSupportedException):
            logger.warning(f'不支持的协议，拒绝代理: {proxy_string} -> {error}')
            return f"不支持的协议: {error.protocol}"
        
        else:
            logger.error(f'未知协议错误: {proxy_string} -> {type(error).__name__}: {error}')
            return f"未知协议错误: {error}"
    
    @staticmethod
    def handle_pool_empty_error(protocol: Optional[str] = None) -> str:
        """
        处理代理池为空错误
        
        :param protocol: 协议类型
        :return: 处理结果消息
        """
        if protocol:
            message = f"{protocol.upper()}代理池为空"
            logger.warning(message)
            return message
        else:
            message = "所有代理池都为空"
            logger.warning(message)
            return message


class RedisErrorHandler:
    """
    Redis相关错误处理器
    处理连接、操作等错误
    """
    
    @staticmethod
    @ErrorHandler.with_retry(max_retries=3, delay=1.0, 
                           exceptions=(RedisConnectionException, ConnectionError))
    async def with_connection_retry(func: Callable, *args, **kwargs):
        """
        带连接重试的Redis操作
        
        :param func: Redis操作函数
        :return: 操作结果
        """
        return await func(*args, **kwargs)
    
    @staticmethod
    def handle_redis_error(operation: str, error: Exception) -> str:
        """
        处理Redis操作错误
        
        :param operation: 操作名称
        :param error: 异常对象
        :return: 处理结果消息
        """
        if isinstance(error, RedisConnectionException):
            message = f"Redis连接失败 ({operation}): {error}"
            logger.error(message)
            return message
        
        elif isinstance(error, ConnectionError):
            message = f"网络连接错误 ({operation}): {error}"
            logger.error(message)
            return message
        
        else:
            message = f"Redis操作失败 ({operation}): {type(error).__name__}: {error}"
            logger.error(message)
            return message


class TesterErrorHandler:
    """
    测试器相关错误处理器
    处理代理测试错误
    """
    
    @staticmethod
    def handle_test_error(proxy_string: str, protocol: str, error: Exception) -> str:
        """
        处理代理测试错误
        
        :param proxy_string: 代理字符串
        :param protocol: 协议类型
        :param error: 异常对象
        :return: 处理结果消息
        """
        if isinstance(error, TesterException):
            message = f"{protocol.upper()}代理测试失败: {proxy_string} -> {error}"
            logger.debug(message)
            return message
        
        elif isinstance(error, asyncio.TimeoutError):
            message = f"{protocol.upper()}代理测试超时: {proxy_string}"
            logger.debug(message)
            return message
        
        elif isinstance(error, ConnectionError):
            message = f"{protocol.upper()}代理连接失败: {proxy_string}"
            logger.debug(message)
            return message
        
        else:
            message = f"{protocol.upper()}代理测试异常: {proxy_string} -> {type(error).__name__}: {error}"
            logger.debug(message)
            return message


class SystemErrorHandler:
    """
    系统级错误处理器
    处理系统级异常和错误恢复
    """
    
    @staticmethod
    def get_error_summary() -> Dict[str, Any]:
        """
        获取错误统计摘要
        
        :return: 错误统计信息
        """
        # 这里可以实现错误统计逻辑
        # 例如从日志中统计各类错误的发生次数
        return {
            "protocol_errors": 0,
            "redis_errors": 0,
            "tester_errors": 0,
            "system_errors": 0,
            "last_check": __import__('time').time()
        }
    
    @staticmethod
    def emergency_shutdown():
        """
        紧急关闭处理
        在系统出现严重错误时调用
        """
        logger.critical("系统紧急关闭中...")
        # 这里可以实现紧急关闭逻辑
        # 例如保存重要数据、关闭连接等
    
    @staticmethod
    def health_check() -> Dict[str, Any]:
        """
        系统健康检查
        
        :return: 健康状态信息
        """
        return {
            "status": "healthy",
            "error_rate": 0.0,
            "last_error": None,
            "uptime": __import__('time').time(),
            "memory_usage": "normal"
        }


# 全局错误处理配置
ERROR_HANDLING_CONFIG = {
    "protocol_detection": {
        "strict_mode": True,  # 严格模式：拒绝无法检测协议的代理
        "log_level": "warning"
    },
    "redis_operations": {
        "max_retries": 3,
        "retry_delay": 1.0,
        "connection_timeout": 5.0
    },
    "proxy_testing": {
        "timeout": 10.0,
        "max_concurrent": 100,
        "log_failures": True
    },
    "system": {
        "emergency_threshold": 0.8,  # 错误率阈值
        "health_check_interval": 60.0
    }
}
