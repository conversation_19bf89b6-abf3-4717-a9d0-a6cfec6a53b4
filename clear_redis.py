import asyncio
from http_proxy_pool.storages.redis_client import AsyncRedisClient

async def clear_redis():
    redis = AsyncRedisClient()
    
    # 获取当前代理数量
    count_before = await redis.count()
    print(f'清理前代理数量: {count_before}')
    
    # 清理所有代理
    await redis.zremrangebyscore(score_min=0, score_max=100)
    
    # 获取清理后代理数量
    count_after = await redis.count()
    print(f'清理后代理数量: {count_after}')
    
    await redis.close()

if __name__ == '__main__':
    asyncio.run(clear_redis())