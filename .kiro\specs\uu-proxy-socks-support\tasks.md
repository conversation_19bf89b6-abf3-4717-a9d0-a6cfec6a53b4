# UU Proxy SOCKS 协议支持实现计划

- [x] 1. 更新配置常量以支持多协议


  - 将单一 SCHEMES 常量替换为支持多协议的配置
  - 添加 SUPPORTED_SCHEMES 和 ENABLED_SCHEMES 列表
  - 确保向后兼容性
  - _需求: 1.1, 2.1, 4.2_

- [x] 2. 实现协议验证和配置获取方法


  - 创建 _get_enabled_schemes() 方法获取启用的协议列表
  - 实现协议有效性验证逻辑
  - 添加默认值处理和错误处理
  - _需求: 4.1, 4.3_

- [x] 3. 实现按协议爬取的核心方法


  - 创建 _crawl_by_scheme(scheme) 方法
  - 实现单个协议的 API 调用和数据解析
  - 添加协议特定的错误处理和日志记录
  - _需求: 1.1, 1.2, 2.1, 2.2, 5.1, 5.2_

- [x] 4. 增强主爬取方法支持多协议轮询


  - 修改 crawl() 方法以支持多协议遍历
  - 实现协议轮询逻辑，确保失败不中断其他协议
  - 添加每个协议的统计计数和汇总日志
  - _需求: 3.1, 3.2, 3.3, 5.3_

- [x] 5. 测试多协议功能并验证代理解析



  - 测试 SOCKS4 协议代理的获取和解析
  - 测试 SOCKS5 协议代理的获取和解析  
  - 验证现有 HTTP 协议功能不受影响
  - 测试错误处理和日志记录功能
  - _需求: 1.3, 2.3, 5.1, 5.2, 5.3_