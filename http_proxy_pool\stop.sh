#!/bin/bash

# 协议感知代理池系统停止脚本

PROJECT_ROOT="D:\onedrive\proxy_pool\http_proxy_pool"
cd "$PROJECT_ROOT"

echo "🛑 停止协议感知代理池系统..."

# 停止各个服务
for service in api getter tester; do
    if [ -f "pids/$service.pid" ]; then
        PID=$(cat "pids/$service.pid")
        if kill -0 $PID 2>/dev/null; then
            echo "停止 $service (PID: $PID)..."
            kill $PID
            sleep 2
            if kill -0 $PID 2>/dev/null; then
                echo "强制停止 $service..."
                kill -9 $PID
            fi
        fi
        rm -f "pids/$service.pid"
    fi
done

echo "✅ 系统已停止"
