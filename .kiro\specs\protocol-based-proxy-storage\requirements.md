# 基于协议的代理存储需求文档

## 介绍

当前代理池系统将所有代理都存储在一个universal表中，不区分协议类型。为了更好地管理和使用不同类型的代理，需要根据代理协议（HTTP、SOCKS4、SOCKS5）将代理分别存储到不同的Redis表中。

## 需求

### 需求 1: 协议分离存储

**用户故事:** 作为代理池管理员，我希望不同协议的代理存储在不同的Redis表中，以便更好地管理和查询特定类型的代理。

#### 验收标准
1. WHEN 系统获取到HTTP代理 THEN 系统 SHALL 将代理存储到 `proxies:http` 表中
2. WHEN 系统获取到SOCKS4代理 THEN 系统 SHALL 将代理存储到 `proxies:socks4` 表中  
3. WHEN 系统获取到SOCKS5代理 THEN 系统 SHALL 将代理存储到 `proxies:socks5` 表中
4. WHEN 代理协议未明确指定 THEN 系统 SHALL 拒绝入库该代理，防止数据库污染

### 需求 2: 代理格式标准化

**用户故事:** 作为开发者，我希望存储在Redis中的代理格式统一为纯IP:端口格式，不包含协议前缀，以便简化代理使用。

#### 验收标准
1. WHEN 代理入库时 THEN 系统 SHALL 移除协议前缀，只保存 `host:port` 格式
2. WHEN 代理包含认证信息时 THEN 系统 SHALL 保留认证信息格式 `user:pass@host:port`
3. WHEN 从Redis获取代理时 THEN 系统 SHALL 返回纯 `host:port` 格式的代理字符串
4. WHEN txt_proxies爬虫从http_urls获取纯ip:port格式代理 THEN 系统 SHALL 自动标记为HTTP协议
5. WHEN txt_proxies爬虫从socks4_urls获取纯ip:port格式代理 THEN 系统 SHALL 自动标记为SOCKS4协议
6. WHEN txt_proxies爬虫从socks5_urls获取纯ip:port格式代理 THEN 系统 SHALL 自动标记为SOCKS5协议

### 需求 3: 协议感知的代理获取

**用户故事:** 作为API用户，我希望能够指定获取特定协议类型的代理，以满足不同的使用场景。

#### 验收标准
1. WHEN 用户请求HTTP代理 THEN 系统 SHALL 从 `proxies:http` 表中返回代理
2. WHEN 用户请求SOCKS4代理 THEN 系统 SHALL 从 `proxies:socks4` 表中返回代理
3. WHEN 用户请求SOCKS5代理 THEN 系统 SHALL 从 `proxies:socks5` 表中返回代理
4. WHEN 用户未指定协议类型 THEN 系统 SHALL 从所有表中随机返回代理

### 需求 4: 向后兼容性

**用户故事:** 作为现有用户，我希望系统在升级后仍能正常工作，不影响现有的代理获取逻辑。

#### 验收标准
1. WHEN 系统升级后 THEN 现有的API接口 SHALL 继续正常工作
2. WHEN 用户使用默认配置 THEN 系统 SHALL 优先从HTTP代理表中获取代理
3. WHEN 存在legacy universal表数据 THEN 系统 SHALL 能够正确迁移到新的分表结构

### 需求 5: 配置灵活性

**用户故事:** 作为系统管理员，我希望能够配置不同协议表的Redis键名，以适应不同的部署环境。

#### 验收标准
1. WHEN 管理员配置HTTP代理表名 THEN 系统 SHALL 使用指定的表名存储HTTP代理
2. WHEN 管理员配置SOCKS代理表名 THEN 系统 SHALL 使用指定的表名存储SOCKS代理
3. WHEN 未配置表名 THEN 系统 SHALL 使用默认的表名格式 `proxies:{protocol}`

### 需求 6: 协议感知的代理测试

**用户故事:** 作为系统管理员，我希望测试器能够根据代理协议类型使用相应的测试方法，确保测试的准确性。

#### 验收标准
1. WHEN 测试HTTP代理 THEN 系统 SHALL 使用HTTP协议格式进行连接测试
2. WHEN 测试SOCKS4代理 THEN 系统 SHALL 使用SOCKS4协议格式进行连接测试
3. WHEN 测试SOCKS5代理 THEN 系统 SHALL 使用SOCKS5协议格式进行连接测试
4. WHEN 所有协议测试 THEN 系统 SHALL 使用统一的测试URL进行连通性验证
5. WHEN 代理测试失败 THEN 系统 SHALL 在对应协议表中降低该代理分数

### 需求 7: 统计和监控

**用户故事:** 作为运维人员，我希望能够分别监控不同协议代理的数量和状态，以便更好地了解代理池状况。

#### 验收标准
1. WHEN 查询代理统计 THEN 系统 SHALL 分别返回HTTP、SOCKS4、SOCKS5代理的数量
2. WHEN 查询代理状态 THEN 系统 SHALL 显示每种协议代理的健康状态分布
3. WHEN 进行代理测试 THEN 系统 SHALL 根据协议类型使用相应的测试方法