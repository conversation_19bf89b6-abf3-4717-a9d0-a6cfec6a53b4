from http_proxy_pool.schemas import Proxy


def _remove_protocol(data: str) -> str:
    """移除协议前缀"""
    return data.split('://', 1)[1] if '://' in data else data


def _extract_protocol(data: str) -> str:
    """提取协议前缀"""
    return data.split('://', 1)[0] if '://' in data else ''


def is_valid_proxy(data):
    """
    check this string is within proxy format
    支持格式：
    - IP:端口 (如：***********:8080)
    - username:password@IP:端口 (如：user:pass@***********:8080)
    - http://IP:端口 (自动去除协议前缀)
    - ***************************:端口 (自动去除协议前缀)
    """
    data = _remove_protocol(data)
    
    if not ':' in data:
        return False  # 必须包含端口
    
    if '@' in data:  # 认证代理
        auth_part, ip_port = data.rsplit('@', 1)
        if ':' not in ip_port:
            return False
        ip, port = ip_port.rsplit(':', 1)
        return is_ip_valid(ip) and is_port_valid(port)
    else:  # 普通代理
        ip, port = data.rsplit(':', 1)
        return is_ip_valid(ip) and is_port_valid(port)


def is_ip_valid(ip):
    """
    check this string is within ip format
    只支持IP地址，不支持域名
    """
    parts = ip.split('.')
    if len(parts) != 4:
        return False
    
    try:
        return all(0 <= int(part) <= 255 and part.isdigit() for part in parts)
    except ValueError:
        return False


def is_port_valid(port):
    """
    检查端口是否有效
    端口范围：1-65535
    """
    try:
        return port.isdigit() and 1 <= int(port) <= 65535
    except ValueError:
        return False


def _parse_single_proxy(item: str) -> Proxy:
    """解析单个代理字符串为Proxy对象"""
    if not is_valid_proxy(item):
        return None
    
    original_item = item.strip()
    protocol = _extract_protocol(original_item)
    data = _remove_protocol(original_item)
    
    # 提取端口
    port = int(data.rsplit(':', 1)[1])
    
    # 构建host（保留协议和认证信息）
    host = original_item.rsplit(':', 1)[0]
    
    return Proxy(host=host, port=port)


def convert_proxy_or_proxies(data):
    """
    convert list of str to valid proxies or proxy
    支持带协议前缀的代理，会保留完整URL作为host
    :param data:
    :return:
    """
    if not data:
        return None
    
    if isinstance(data, list):
        result = []
        for item in data:
            proxy = _parse_single_proxy(item)
            if proxy:
                result.append(proxy)
        return result
    
    if isinstance(data, str):
        return _parse_single_proxy(data)



