"""
协议感知的代理测试器
支持HTTP、SOCKS4、SOCKS5代理的专门测试方法
使用统一的测试URL但不同的连接方式
"""

import asyncio
import aiohttp
from typing import Dict, List, Optional
from loguru import logger

from http_proxy_pool.schemas.proxy import Proxy
from http_proxy_pool.utils.protocol_detector import ProtocolType
from http_proxy_pool.exceptions import ProtocolNotSupportedException, TesterException
from http_proxy_pool.setting import TEST_TIMEOUT, TEST_URL, TEST_VALID_STATUS

# 尝试导入aiohttp-socks，如果没有安装则记录警告
try:
    from aiohttp_socks import ProxyConnector
    SOCKS_SUPPORT = True
except ImportError:
    SOCKS_SUPPORT = False
    logger.warning("aiohttp-socks未安装，SOCKS代理测试将被禁用。请运行: pip install aiohttp-socks")


class ProtocolAwareTester:
    """
    协议感知的代理测试器
    根据代理协议类型使用不同的连接方法进行测试
    """
    
    def __init__(self, test_url: str = TEST_URL, timeout: int = TEST_TIMEOUT, 
                 valid_status_codes: List[int] = None):
        """
        初始化协议感知测试器
        
        :param test_url: 统一的测试URL
        :param timeout: 测试超时时间
        :param valid_status_codes: 有效的HTTP状态码列表
        """
        self.test_url = test_url
        self.timeout = timeout
        self.valid_status_codes = valid_status_codes or TEST_VALID_STATUS
        
        # 性能统计
        self.stats = {
            'http_tested': 0,
            'https_tested': 0,
            'socks4_tested': 0,
            'socks5_tested': 0,
            'http_success': 0,
            'https_success': 0,
            'socks4_success': 0,
            'socks5_success': 0,
        }
    
    async def test_http_proxy(self, proxy: Proxy) -> bool:
        """
        测试HTTP代理
        使用标准的aiohttp代理参数
        
        :param proxy: 代理对象
        :return: 测试是否成功
        """
        self.stats['http_tested'] += 1
        
        try:
            # 构建HTTP代理URL
            proxy_url = f"http://{proxy.get_normalized_string()}"
            
            # 创建连接器，禁用SSL验证以提高性能
            connector = aiohttp.TCPConnector(ssl=False, limit=100, limit_per_host=30)
            
            async with aiohttp.ClientSession(connector=connector) as session:
                async with session.get(
                    self.test_url,
                    proxy=proxy_url,
                    timeout=aiohttp.ClientTimeout(total=self.timeout),
                    allow_redirects=False
                ) as response:
                    success = response.status in self.valid_status_codes
                    if success:
                        self.stats['http_success'] += 1
                    
                    logger.debug(f'HTTP代理测试: {proxy.get_normalized_string()} -> 状态码 {response.status}, 成功: {success}')
                    return success
                    
        except Exception as e:
            logger.debug(f'HTTP代理测试失败: {proxy.get_normalized_string()} -> {type(e).__name__}: {str(e)[:100]}')
            return False
    
    async def test_https_proxy(self, proxy: Proxy) -> bool:
        """
        测试HTTPS代理
        使用HTTPS代理URL格式
        
        :param proxy: 代理对象
        :return: 测试是否成功
        """
        self.stats['https_tested'] += 1
        
        try:
            # 构建HTTPS代理URL
            proxy_url = f"https://{proxy.get_normalized_string()}"
            
            connector = aiohttp.TCPConnector(ssl=False, limit=100, limit_per_host=30)
            
            async with aiohttp.ClientSession(connector=connector) as session:
                async with session.get(
                    self.test_url,
                    proxy=proxy_url,
                    timeout=aiohttp.ClientTimeout(total=self.timeout),
                    allow_redirects=False
                ) as response:
                    success = response.status in self.valid_status_codes
                    if success:
                        self.stats['https_success'] += 1
                    
                    logger.debug(f'HTTPS代理测试: {proxy.get_normalized_string()} -> 状态码 {response.status}, 成功: {success}')
                    return success
                    
        except Exception as e:
            logger.debug(f'HTTPS代理测试失败: {proxy.get_normalized_string()} -> {type(e).__name__}: {str(e)[:100]}')
            return False
    
    async def test_socks4_proxy(self, proxy: Proxy) -> bool:
        """
        测试SOCKS4代理
        使用aiohttp-socks库的SOCKS4连接器
        
        :param proxy: 代理对象
        :return: 测试是否成功
        """
        if not SOCKS_SUPPORT:
            logger.warning(f'SOCKS支持未启用，跳过SOCKS4代理测试: {proxy.get_normalized_string()}')
            return False
        
        self.stats['socks4_tested'] += 1
        
        try:
            # 构建SOCKS4代理URL
            proxy_url = f"socks4://{proxy.get_normalized_string()}"
            
            # 创建SOCKS4连接器
            connector = ProxyConnector.from_url(proxy_url)
            
            async with aiohttp.ClientSession(connector=connector) as session:
                async with session.get(
                    self.test_url,
                    timeout=aiohttp.ClientTimeout(total=self.timeout),
                    allow_redirects=False
                ) as response:
                    success = response.status in self.valid_status_codes
                    if success:
                        self.stats['socks4_success'] += 1
                    
                    logger.debug(f'SOCKS4代理测试: {proxy.get_normalized_string()} -> 状态码 {response.status}, 成功: {success}')
                    return success
                    
        except Exception as e:
            logger.debug(f'SOCKS4代理测试失败: {proxy.get_normalized_string()} -> {type(e).__name__}: {str(e)[:100]}')
            return False
    
    async def test_socks5_proxy(self, proxy: Proxy) -> bool:
        """
        测试SOCKS5代理
        使用aiohttp-socks库的SOCKS5连接器
        
        :param proxy: 代理对象
        :return: 测试是否成功
        """
        if not SOCKS_SUPPORT:
            logger.warning(f'SOCKS支持未启用，跳过SOCKS5代理测试: {proxy.get_normalized_string()}')
            return False
        
        self.stats['socks5_tested'] += 1
        
        try:
            # 构建SOCKS5代理URL（支持认证）
            if proxy.username and proxy.password:
                proxy_url = f"socks5://{proxy.username}:{proxy.password}@{proxy.get_normalized_string()}"
            else:
                proxy_url = f"socks5://{proxy.get_normalized_string()}"
            
            # 创建SOCKS5连接器
            connector = ProxyConnector.from_url(proxy_url)
            
            async with aiohttp.ClientSession(connector=connector) as session:
                async with session.get(
                    self.test_url,
                    timeout=aiohttp.ClientTimeout(total=self.timeout),
                    allow_redirects=False
                ) as response:
                    success = response.status in self.valid_status_codes
                    if success:
                        self.stats['socks5_success'] += 1
                    
                    logger.debug(f'SOCKS5代理测试: {proxy.get_normalized_string()} -> 状态码 {response.status}, 成功: {success}')
                    return success
                    
        except Exception as e:
            logger.debug(f'SOCKS5代理测试失败: {proxy.get_normalized_string()} -> {type(e).__name__}: {str(e)[:100]}')
            return False
    
    async def test_proxy_by_protocol(self, proxy: Proxy) -> bool:
        """
        根据协议类型测试代理
        
        :param proxy: 代理对象
        :return: 测试是否成功
        :raises ProtocolNotSupportedException: 不支持的协议类型
        """
        protocol_enum = proxy.get_protocol_enum()
        
        if protocol_enum == ProtocolType.HTTP:
            return await self.test_http_proxy(proxy)
        elif protocol_enum == ProtocolType.HTTPS:
            return await self.test_https_proxy(proxy)
        elif protocol_enum == ProtocolType.SOCKS4:
            return await self.test_socks4_proxy(proxy)
        elif protocol_enum == ProtocolType.SOCKS5:
            return await self.test_socks5_proxy(proxy)
        else:
            raise ProtocolNotSupportedException(f"不支持的协议: {proxy.protocol}")
    
    async def test_proxy_batch(self, proxies: List[Proxy]) -> Dict[str, bool]:
        """
        批量测试代理（高性能版本）
        
        :param proxies: 代理列表
        :return: 代理字符串到测试结果的映射
        """
        if not proxies:
            return {}
        
        logger.info(f'开始批量测试 {len(proxies)} 个代理')
        
        # 创建测试任务
        tasks = []
        proxy_keys = []
        
        for proxy in proxies:
            task = self.test_proxy_by_protocol(proxy)
            tasks.append(task)
            proxy_keys.append(proxy.get_normalized_string())
        
        # 并发执行所有测试
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        test_results = {}
        success_count = 0
        error_count = 0
        
        for i, result in enumerate(results):
            proxy_key = proxy_keys[i]
            if isinstance(result, Exception):
                test_results[proxy_key] = False
                error_count += 1
                logger.debug(f'代理测试异常: {proxy_key} -> {type(result).__name__}: {str(result)[:100]}')
            else:
                test_results[proxy_key] = result
                if result:
                    success_count += 1
        
        logger.info(f'批量测试完成: 成功 {success_count}, 失败 {len(proxies) - success_count}, 异常 {error_count}')
        
        return test_results
    
    def get_stats(self) -> Dict[str, int]:
        """
        获取测试统计信息
        
        :return: 统计信息字典
        """
        # 计算成功率
        stats_with_rates = self.stats.copy()
        
        for protocol in ['http', 'https', 'socks4', 'socks5']:
            tested_key = f'{protocol}_tested'
            success_key = f'{protocol}_success'
            rate_key = f'{protocol}_success_rate'
            
            tested = stats_with_rates.get(tested_key, 0)
            success = stats_with_rates.get(success_key, 0)
            
            if tested > 0:
                stats_with_rates[rate_key] = round(success / tested * 100, 2)
            else:
                stats_with_rates[rate_key] = 0.0
        
        return stats_with_rates
    
    def reset_stats(self):
        """重置统计信息"""
        for key in self.stats:
            self.stats[key] = 0
