name: build
on:
  push:
    branches: 
      - master
    paths-ignore:
      - .gitignore
      - README.md
      - '.github/ISSUE_TEMPLATE/**'
    release:
      types: [published]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Login to DockerHub
        uses: docker/login-action@v1
        with:
          username: germey
          password: ${{ secrets.DOCKERHUB_LOGIN_PASSWORD }}
      
      - name: Get current date
        id: date
        run: echo "::set-output name=date::$(date +'%Y%m%d')"
      
      - name: Build and push
        uses: docker/build-push-action@v2
        with:
          context: .
          push: true
          platforms: linux/amd64
          tags: |
            germey/proxypool:latest
            germey/proxypool:master
            germey/proxypool:${{ steps.date.outputs.date }}
