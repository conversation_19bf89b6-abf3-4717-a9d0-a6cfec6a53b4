"""
高性能协议检测和标准化工具
专为代理池系统优化，避免性能瓶颈
"""

import re
from typing import Optional, Tuple, Dict
from enum import Enum
from functools import lru_cache


class ProtocolType(Enum):
    """支持的协议类型"""
    HTTP = "http"
    HTTPS = "https"
    SOCKS4 = "socks4"
    SOCKS5 = "socks5"


# 导入统一的异常类
from http_proxy_pool.exceptions import ProtocolDetectionException


class ProtocolDetector:
    """
    高性能协议检测器
    使用缓存和优化的字符串操作提升性能
    """
    
    # 预编译正则表达式，避免重复编译
    _PROTOCOL_PATTERN = re.compile(r'^(https?|socks[45])://', re.IGNORECASE)
    _HOST_PORT_PATTERN = re.compile(r'^([^:]+):(\d+)$')
    _AUTH_PATTERN = re.compile(r'^([^:]+):([^@]+)@(.+)$')
    
    # URL类型到协议的映射（用于txt_proxies推断）
    _URL_TYPE_TO_PROTOCOL: Dict[str, ProtocolType] = {
        'http_urls': ProtocolType.HTTP,
        'socks4_urls': ProtocolType.SOCKS4,
        'socks5_urls': ProtocolType.SOCKS5,
    }
    
    @classmethod
    @lru_cache(maxsize=1024)  # 缓存常见的协议检测结果
    def detect_protocol_from_string(cls, proxy_string: str) -> Optional[ProtocolType]:
        """
        从代理字符串检测协议
        
        支持格式:
        - http://host:port
        - https://host:port
        - socks4://host:port
        - socks5://host:port
        - *********************:port
        
        :param proxy_string: 代理字符串
        :return: 协议类型，如果无法检测返回None
        """
        if not proxy_string:
            return None
            
        proxy_string = proxy_string.strip()
        
        # 快速检查是否包含协议前缀
        match = cls._PROTOCOL_PATTERN.match(proxy_string)
        if match:
            protocol_str = match.group(1).lower()
            try:
                return ProtocolType(protocol_str)
            except ValueError:
                return None
        
        return None
    
    @classmethod
    def infer_protocol_from_source(cls, url_type: str) -> ProtocolType:
        """
        根据来源URL类型推断协议（用于txt_proxies爬虫）
        
        :param url_type: URL类型 ('http_urls', 'socks4_urls', 'socks5_urls')
        :return: 推断的协议类型
        :raises ProtocolDetectionException: 无法推断协议时抛出
        """
        protocol = cls._URL_TYPE_TO_PROTOCOL.get(url_type)
        if protocol is None:
            raise ProtocolDetectionException(f"Cannot infer protocol from source: {url_type}")
        return protocol
    
    @classmethod
    @lru_cache(maxsize=512)  # 缓存标准化结果
    def normalize_proxy_string(cls, proxy_string: str) -> str:
        """
        标准化代理字符串，移除协议前缀，保留纯host:port格式
        
        :param proxy_string: 原始代理字符串
        :return: 标准化后的host:port格式
        """
        if not proxy_string:
            return proxy_string
            
        proxy_string = proxy_string.strip()
        
        # 移除协议前缀
        if '://' in proxy_string:
            proxy_string = proxy_string.split('://', 1)[1]
        
        return proxy_string
    
    @classmethod
    def parse_proxy_components(cls, proxy_string: str) -> Tuple[Optional[str], Optional[int], Optional[str], Optional[str]]:
        """
        解析代理字符串的各个组件
        
        :param proxy_string: 代理字符串
        :return: (host, port, username, password) 元组
        """
        if not proxy_string:
            return None, None, None, None
        
        # 标准化字符串
        normalized = cls.normalize_proxy_string(proxy_string)
        
        username = None
        password = None
        
        # 检查是否包含认证信息
        if '@' in normalized:
            auth_match = cls._AUTH_PATTERN.match(normalized)
            if auth_match:
                username = auth_match.group(1)
                password = auth_match.group(2)
                host_port = auth_match.group(3)
            else:
                # 简单分割方式作为后备
                auth_part, host_port = normalized.rsplit('@', 1)
                if ':' in auth_part:
                    username, password = auth_part.split(':', 1)
                else:
                    username = auth_part
        else:
            host_port = normalized
        
        # 解析host和port
        host_port_match = cls._HOST_PORT_PATTERN.match(host_port)
        if host_port_match:
            host = host_port_match.group(1)
            try:
                port = int(host_port_match.group(2))
                # 验证端口范围
                if not (1 <= port <= 65535):
                    return None, None, None, None
            except ValueError:
                return None, None, None, None
        else:
            return None, None, None, None
        
        return host, port, username, password
    
    @classmethod
    def validate_protocol_string(cls, proxy_string: str, expected_protocol: ProtocolType) -> bool:
        """
        验证代理字符串是否符合指定协议格式
        
        :param proxy_string: 代理字符串
        :param expected_protocol: 期望的协议类型
        :return: 是否符合格式
        """
        detected_protocol = cls.detect_protocol_from_string(proxy_string)
        
        # 如果检测到协议，必须匹配期望协议
        if detected_protocol is not None:
            return detected_protocol == expected_protocol
        
        # 如果没有检测到协议，检查基本格式是否有效
        host, port, _, _ = cls.parse_proxy_components(proxy_string)
        return host is not None and port is not None
    
    @classmethod
    def get_redis_key_for_protocol(cls, protocol: ProtocolType, base_key: str = "proxies") -> str:
        """
        根据协议类型生成Redis键
        
        :param protocol: 协议类型
        :param base_key: 基础键名
        :return: 协议特定的Redis键
        """
        return f"{base_key}:{protocol.value}"
    
    @classmethod
    def get_all_protocol_keys(cls, base_key: str = "proxies") -> Dict[ProtocolType, str]:
        """
        获取所有协议对应的Redis键
        
        :param base_key: 基础键名
        :return: 协议到Redis键的映射
        """
        return {
            protocol: cls.get_redis_key_for_protocol(protocol, base_key)
            for protocol in ProtocolType
        }


class ProtocolStandardizer:
    """
    协议标准化工具
    专门处理代理字符串的标准化操作
    """
    
    @staticmethod
    def standardize_for_storage(proxy_string: str, protocol: ProtocolType) -> str:
        """
        为存储标准化代理字符串
        移除协议前缀，保留纯host:port格式用于Redis存储
        
        :param proxy_string: 原始代理字符串
        :param protocol: 协议类型
        :return: 标准化的存储格式
        """
        return ProtocolDetector.normalize_proxy_string(proxy_string)
    
    @staticmethod
    def standardize_for_usage(host: str, port: int, protocol: ProtocolType, 
                            username: Optional[str] = None, password: Optional[str] = None) -> str:
        """
        为使用标准化代理字符串
        生成完整的协议URL格式用于实际代理连接
        
        :param host: 主机地址
        :param port: 端口
        :param protocol: 协议类型
        :param username: 用户名（可选）
        :param password: 密码（可选）
        :return: 完整的代理URL
        """
        if username and password:
            return f"{protocol.value}://{username}:{password}@{host}:{port}"
        elif username:
            return f"{protocol.value}://{username}@{host}:{port}"
        else:
            return f"{protocol.value}://{host}:{port}"
