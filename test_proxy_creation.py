from http_proxy_pool.schemas.proxy import Proxy

# 测试修复后的代理创建
proxy1 = Proxy(host='http://***********', port=8080)
proxy2 = Proxy(host='socks5://user:pass@***********', port=1080)

print(f"Proxy 1 - host: {proxy1.host}, port: {proxy1.port}")
print(f"Proxy 1 - string(): {proxy1.string()}")
print(f"Proxy 1 - full_string(): {proxy1.full_string()}")

print(f"Proxy 2 - host: {proxy2.host}, port: {proxy2.port}")
print(f"Proxy 2 - string(): {proxy2.string()}")
print(f"Proxy 2 - full_string(): {proxy2.full_string()}")