{"rustc": 16591470773350601817, "features": "[\"client\", \"default\", \"http1\", \"runtime\", \"server\", \"socket2\", \"tcp\"]", "declared_features": "[\"__internal_happy_eyeballs_tests\", \"client\", \"default\", \"ffi\", \"full\", \"h2\", \"http1\", \"http2\", \"libc\", \"nightly\", \"runtime\", \"server\", \"socket2\", \"stream\", \"tcp\"]", "target": 12386857176296857293, "profile": 2241668132362809309, "path": 12257103111270190500, "deps": [[1768984221433149204, "futures_channel", false, 9085917385578827938], [1952079052086493257, "httparse", false, 11976274255132549992], [4181961464061095916, "tracing", false, 12420660327984102414], [4658011345517094182, "tower_service", false, 13791098653544855103], [4748747762575608935, "futures_util", false, 1063530787091940909], [8147002042506387757, "http_body", false, 18046475974343434744], [8500831766667767322, "futures_core", false, 3487440895221454149], [8671496910876289932, "want", false, 2834003452834435680], [9033580981096220970, "socket2", false, 15010071351280958808], [10538505878868246922, "http", false, 17294136693532182630], [13633450820682967255, "httpdate", false, 12930883425169978437], [15470534839312576504, "pin_project_lite", false, 7201407369536850343], [16102016720778300874, "itoa", false, 14518197434798439942], [16788796250767550975, "tokio", false, 10697472201211039662], [17464000126263649914, "bytes", false, 7920405009441532808]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-7f5da67f67fc3612\\dep-lib-hyper", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}