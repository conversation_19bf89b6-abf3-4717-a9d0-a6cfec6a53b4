"""
协议感知的API服务器
支持按协议获取代理、协议统计、智能路由等功能
"""

from fastapi import FastAPI, HTTPException, Depends, Query, Path
from fastapi.responses import PlainTextResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from typing import Optional, List
import functools
from loguru import logger

from http_proxy_pool.exceptions import PoolEmptyException, ProtocolNotSupportedException
from http_proxy_pool.storages.protocol_aware_redis_client import ProtocolAwareRedisClient
from http_proxy_pool.routers.protocol_router import ProtocolRouter
from http_proxy_pool.utils.protocol_detector import ProtocolType
from http_proxy_pool.setting import (
    API_HOST, API_PORT, API_KEY, IS_DEV, PROXY_SCORE_MAX
)


# 全局变量
redis_client = None
protocol_router = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global redis_client, protocol_router
    
    # 启动时初始化
    logger.info("协议感知代理池API服务器启动中...")
    redis_client = ProtocolAwareRedisClient()
    protocol_router = ProtocolRouter(redis_client)
    logger.info("协议感知代理池API服务器启动完成")
    
    yield
    
    # 关闭时清理
    if redis_client:
        await redis_client.close()
    logger.info("协议感知代理池API服务器关闭完成")


# 创建FastAPI应用
app = FastAPI(
    title="协议感知代理池API",
    description="支持HTTP、SOCKS4、SOCKS5协议的高性能代理池API服务",
    version="3.0.0",
    lifespan=lifespan,
    debug=IS_DEV
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=False,
    allow_methods=["GET", "POST", "HEAD", "OPTIONS"],
    allow_headers=["Content-Type", "API-KEY", "User-Agent", "Accept", "Authorization"],
    expose_headers=["Content-Length", "Content-Type"],
    max_age=3600,
)


async def auth_dependency(api_key: Optional[str] = Query(None, alias="API-KEY")):
    """API认证依赖"""
    if not API_KEY:
        return True

    if api_key is None:
        raise HTTPException(status_code=400, detail="请在查询参数中提供API密钥")

    if api_key != API_KEY:
        raise HTTPException(status_code=403, detail="提供的API密钥无效")

    return True


async def get_router():
    """获取协议路由器依赖"""
    if protocol_router is None:
        raise HTTPException(status_code=503, detail="协议路由器未初始化")
    return protocol_router


@app.get("/", response_class=JSONResponse)
async def index(_: bool = Depends(auth_dependency)):
    """
    API首页 - 显示服务信息
    """
    return {
        "service": "协议感知代理池API",
        "version": "3.0.0",
        "supported_protocols": ["http", "https", "socks4", "socks5"],
        "endpoints": {
            "random": "获取随机代理",
            "protocol/{protocol}": "获取指定协议代理",
            "batch": "批量获取代理",
            "stats": "获取协议统计",
            "health": "健康检查"
        }
    }


@app.get("/random", response_class=PlainTextResponse)
async def get_random_proxy(
    protocol: Optional[str] = Query(None, description="指定协议类型 (http/https/socks4/socks5)"),
    preferred: Optional[str] = Query(None, description="优先协议列表，逗号分隔"),
    _: bool = Depends(auth_dependency),
    router: ProtocolRouter = Depends(get_router)
):
    """
    获取随机代理（协议感知）
    支持指定协议或优先协议列表
    """
    try:
        preferred_protocols = None
        if preferred:
            preferred_protocols = [p.strip().lower() for p in preferred.split(',')]
        
        proxy = await router.route_get_request(protocol, preferred_protocols)
        return proxy.get_normalized_string()
    
    except ProtocolNotSupportedException as e:
        raise HTTPException(status_code=400, detail=f"不支持的协议: {e.protocol}")
    except PoolEmptyException:
        raise HTTPException(status_code=404, detail="没有可用的代理")
    except Exception as e:
        logger.error(f"获取随机代理失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@app.get("/protocol/{protocol_name}", response_class=PlainTextResponse)
async def get_proxy_by_protocol(
    protocol_name: str = Path(..., description="协议类型"),
    _: bool = Depends(auth_dependency),
    router: ProtocolRouter = Depends(get_router)
):
    """
    获取指定协议的代理
    """
    try:
        proxy = await router.route_get_request(protocol_name)
        return proxy.get_normalized_string()
    
    except ProtocolNotSupportedException:
        raise HTTPException(status_code=400, detail=f"不支持的协议: {protocol_name}")
    except PoolEmptyException:
        raise HTTPException(status_code=404, detail=f"没有可用的{protocol_name.upper()}代理")
    except Exception as e:
        logger.error(f"获取{protocol_name}代理失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@app.get("/batch", response_class=PlainTextResponse)
async def get_batch_proxies(
    count: int = Query(10, description="获取数量", ge=1, le=1000),
    protocol: Optional[str] = Query(None, description="指定协议类型"),
    preferred: Optional[str] = Query(None, description="优先协议列表，逗号分隔"),
    _: bool = Depends(auth_dependency),
    router: ProtocolRouter = Depends(get_router)
):
    """
    批量获取代理
    """
    try:
        preferred_protocols = None
        if preferred:
            preferred_protocols = [p.strip().lower() for p in preferred.split(',')]
        
        proxies = await router.route_batch_get_request(count, protocol, preferred_protocols)
        
        if not proxies:
            raise HTTPException(status_code=404, detail="没有可用的代理")
        
        return '\n'.join(proxy.get_normalized_string() for proxy in proxies)
    
    except ProtocolNotSupportedException:
        raise HTTPException(status_code=400, detail=f"不支持的协议: {protocol}")
    except Exception as e:
        logger.error(f"批量获取代理失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@app.get("/all/{protocol_name}", response_class=PlainTextResponse)
async def get_all_proxies_by_protocol(
    protocol_name: str = Path(..., description="协议类型"),
    _: bool = Depends(auth_dependency),
    router: ProtocolRouter = Depends(get_router)
):
    """
    获取指定协议的所有代理
    """
    try:
        protocol_enum = ProtocolType(protocol_name.lower())
        proxies = await redis_client.get_all_by_protocol(protocol_enum)
        
        if not proxies:
            return ""
        
        return '\n'.join(proxy.get_normalized_string() for proxy in proxies)
    
    except ValueError:
        raise HTTPException(status_code=400, detail=f"不支持的协议: {protocol_name}")
    except Exception as e:
        logger.error(f"获取{protocol_name}所有代理失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@app.get("/valid/{protocol_name}", response_class=PlainTextResponse)
async def get_valid_proxies_by_protocol(
    protocol_name: str = Path(..., description="协议类型"),
    _: bool = Depends(auth_dependency),
    router: ProtocolRouter = Depends(get_router)
):
    """
    获取指定协议的所有有效代理（高分代理）
    """
    try:
        protocol_enum = ProtocolType(protocol_name.lower())
        proxies = await redis_client.get_all_by_protocol(
            protocol_enum, PROXY_SCORE_MAX, PROXY_SCORE_MAX
        )
        
        if not proxies:
            return ""
        
        return '\n'.join(proxy.get_normalized_string() for proxy in proxies)
    
    except ValueError:
        raise HTTPException(status_code=400, detail=f"不支持的协议: {protocol_name}")
    except Exception as e:
        logger.error(f"获取{protocol_name}有效代理失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@app.get("/count", response_class=JSONResponse)
async def get_proxy_counts(
    _: bool = Depends(auth_dependency),
    router: ProtocolRouter = Depends(get_router)
):
    """
    获取各协议代理数量统计
    """
    try:
        counts = await redis_client.count_by_protocol()
        total = sum(counts.values())
        
        return {
            "total": total,
            "protocols": counts
        }
    
    except Exception as e:
        logger.error(f"获取代理数量失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@app.get("/count/{protocol_name}", response_class=PlainTextResponse)
async def get_protocol_count(
    protocol_name: str = Path(..., description="协议类型"),
    _: bool = Depends(auth_dependency),
    router: ProtocolRouter = Depends(get_router)
):
    """
    获取指定协议的代理数量
    """
    try:
        protocol_enum = ProtocolType(protocol_name.lower())
        counts = await redis_client.count_by_protocol(protocol_enum)
        count = counts.get(protocol_name.lower(), 0)
        return str(count)
    
    except ValueError:
        raise HTTPException(status_code=400, detail=f"不支持的协议: {protocol_name}")
    except Exception as e:
        logger.error(f"获取{protocol_name}代理数量失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@app.get("/stats", response_class=JSONResponse)
async def get_protocol_statistics(
    _: bool = Depends(auth_dependency),
    router: ProtocolRouter = Depends(get_router)
):
    """
    获取详细的协议统计信息
    """
    try:
        stats = await router.get_protocol_statistics()
        return stats
    
    except Exception as e:
        logger.error(f"获取协议统计失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@app.get("/health", response_class=JSONResponse)
async def health_check(
    _: bool = Depends(auth_dependency),
    router: ProtocolRouter = Depends(get_router)
):
    """
    健康检查接口
    """
    try:
        health = await router.health_check()
        
        # 根据健康状态设置HTTP状态码
        status_code = 200
        if health.get('status') == 'warning':
            status_code = 206  # Partial Content
        elif health.get('status') == 'error':
            status_code = 503  # Service Unavailable
        
        return JSONResponse(content=health, status_code=status_code)
    
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            content={"status": "error", "error": str(e)},
            status_code=503
        )


@app.post("/cleanup", response_class=JSONResponse)
async def cleanup_invalid_proxies(
    _: bool = Depends(auth_dependency),
    router: ProtocolRouter = Depends(get_router)
):
    """
    清理无效代理
    """
    try:
        result = await router.cleanup_invalid_proxies()
        return {"message": "清理完成", "result": result}
    
    except Exception as e:
        logger.error(f"清理无效代理失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


@app.post("/migrate", response_class=JSONResponse)
async def migrate_from_universal(
    universal_key: str = Query("proxies", description="通用表Redis键"),
    _: bool = Depends(auth_dependency),
    router: ProtocolRouter = Depends(get_router)
):
    """
    从通用表迁移数据到协议分表
    """
    try:
        result = await router.migrate_from_universal_table(universal_key)
        return {"message": "迁移完成", "result": result}
    
    except Exception as e:
        logger.error(f"数据迁移失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


# 兼容性接口（保持与旧版本的兼容性）
@app.get("/all", response_class=PlainTextResponse)
async def get_all_proxies_legacy(
    _: bool = Depends(auth_dependency),
    router: ProtocolRouter = Depends(get_router)
):
    """
    获取所有代理（兼容性接口）
    """
    try:
        # 从所有协议获取代理
        all_proxies = []
        for protocol in ProtocolType:
            try:
                proxies = await redis_client.get_all_by_protocol(protocol)
                if proxies:
                    all_proxies.extend(proxies)
            except PoolEmptyException:
                continue
        
        if not all_proxies:
            return ""
        
        return '\n'.join(proxy.get_normalized_string() for proxy in all_proxies)
    
    except Exception as e:
        logger.error(f"获取所有代理失败: {e}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


if __name__ == '__main__':
    import uvicorn
    
    logger.info(f'启动协议感知代理池API服务器 (模式: {"开发" if IS_DEV else "生产"})')
    
    if IS_DEV:
        uvicorn.run(
            "protocol_aware_server:app",
            host=API_HOST,
            port=API_PORT,
            reload=True,
            log_level="debug"
        )
    else:
        uvicorn.run(
            app,
            host=API_HOST,
            port=API_PORT,
            workers=1,
            log_level="info"
        )