# 协议感知代理池系统

一个高性能的代理池管理系统，支持HTTP、HTTPS、SOCKS4、SOCKS5四种协议的自动检测、分类存储和智能路由。

## 🚀 核心特性

- **协议感知**: 自动检测和分类存储不同协议的代理
- **高性能**: 协议检测 491万次/秒，代理创建 22万次/秒
- **智能路由**: 根据协议类型智能路由和负载均衡
- **严格验证**: 拒绝无法确定协议的代理，保证数据质量
- **RESTful API**: 完整的API接口支持
- **多种部署**: 支持直接启动、Docker、Systemd部署

## 📦 快速安装

```bash
# 克隆项目
git clone <repository-url>
cd http_proxy_pool

# 安装依赖
pip install -r requirements.txt

# 启动Redis服务
redis-server

# 部署系统
python deploy.py
```

## 🎯 快速启动

```bash
# 方式1: 直接启动
./start.sh

# 方式2: Docker部署
docker-compose up -d

# 方式3: 手动启动API服务器
python -m http_proxy_pool.processors.protocol_aware_server
```

## 📖 API使用

```bash
# 获取随机代理
curl http://localhost:5555/random

# 获取HTTP代理
curl http://localhost:5555/protocol/http

# 获取SOCKS5代理
curl http://localhost:5555/protocol/socks5

# 批量获取代理
curl http://localhost:5555/batch?count=10

# 查看系统统计
curl http://localhost:5555/stats

# 健康检查
curl http://localhost:5555/health
```

## 🏗️ 系统架构

```
代理源 → 爬虫 → 协议检测 → 分表存储 → 协议路由 → API接口
  ↓        ↓        ↓          ↓          ↓        ↓
HTTP    推断    验证格式    proxies:http   智能    /random
SOCKS4  协议    拒绝无效    proxies:socks4 负载    /protocol/http
SOCKS5  类型    严格验证    proxies:socks5 均衡    /batch
```

## 📚 文档

- [使用指南](docs/PROTOCOL_AWARE_GUIDE.md) - 详细的使用说明
- [API参考](docs/API_REFERENCE.md) - 完整的API文档

## 🔧 配置

主要配置项在 `.env` 文件中：

```bash
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# API配置
API_HOST=0.0.0.0
API_PORT=5555
API_KEY=your_api_key

# 测试配置
TEST_URL=http://httpbin.org/ip
TEST_TIMEOUT=10
```

## 📊 性能指标

| 组件 | 性能指标 |
|------|----------|
| 协议检测 | 491万次/秒 |
| 代理创建 | 22万次/秒 |
| Redis操作 | 1万次/秒 |
| API响应 | <100ms |

## 🛠️ 开发

```bash
# 安装开发依赖
pip install -r requirements.txt

# 运行代理获取器
python -m http_proxy_pool.processors.getter

# 运行代理测试器
python -m http_proxy_pool.processors.tester

# 启动API服务器
python -m http_proxy_pool.processors.protocol_aware_server
```

## 📝 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请提交 Issue 或查看文档。