from fastapi import FastAPI, HTTPException, Depends, Query
from fastapi.responses import PlainTextResponse, HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from typing import Optional
import functools
from loguru import logger

from http_proxy_pool.exceptions import PoolEmptyException
from http_proxy_pool.storages.redis_client import get_async_redis_client, close_async_redis_client
from http_proxy_pool.setting import (
    API_HOST, API_PORT, API_THREADED, API_KEY, IS_DEV, 
    PROXY_RAND_KEY_DEGRADED, PROXY_SCORE_MAX
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("FastAPI Proxy Pool Server starting...")
    yield
    # 关闭时清理
    await close_async_redis_client()
    logger.info("FastAPI Proxy Pool Server shutdown complete")


# 创建FastAPI应用
app = FastAPI(
    title="Proxy Pool API",
    description="高性能代理池API服务",
    version="2.0.0",
    lifespan=lifespan,
    debug=IS_DEV
)

# 添加CORS中间件 - 代理池API服务器专用配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],              # API服务器允许所有来源访问
    allow_credentials=False,          # 代理API不需要cookies认证
    allow_methods=["GET", "HEAD", "OPTIONS"],  # 只允许读取操作
    allow_headers=[
        "Content-Type",
        "API-KEY",                    # 你的API认证头
        "User-Agent",
        "Accept",
        "Authorization"               # 备用认证头
    ],
    expose_headers=["Content-Length", "Content-Type"],
    max_age=3600,                     # 缓存预检请求1小时，减少客户端请求
)


def auth_required(func):
    """API认证装饰器"""
    @functools.wraps(func)
    async def decorator(*args, **kwargs):
        # 如果没有设置API_KEY，跳过认证
        if API_KEY == "":
            return await func(*args, **kwargs)
        
        # 这里需要从请求头获取API-KEY，在FastAPI中通过依赖注入实现
        # 实际的认证逻辑在auth_dependency中处理
        return await func(*args, **kwargs)
    return decorator


async def auth_dependency(api_key: Optional[str] = Query(None, alias="API-KEY")):
    """API认证依赖 - 修复认证逻辑"""
    # 如果没有设置API_KEY，跳过认证
    if not API_KEY:  # 空字符串或None都跳过认证
        return True

    # 如果设置了API_KEY，但请求没有提供
    if api_key is None:
        raise HTTPException(status_code=400, detail="Please provide an API key in query parameter")

    # 验证API_KEY
    if api_key != API_KEY:
        raise HTTPException(status_code=403, detail="The provided API key is not valid")

    return True


async def get_redis_client():
    """获取Redis客户端依赖 - 优化并发性能"""
    try:
        return await get_async_redis_client()
    except Exception as e:
        logger.error(f"Failed to get Redis client: {e}")
        raise HTTPException(status_code=503, detail="Redis service unavailable")


@app.get("/", response_class=HTMLResponse)
async def index(_: bool = Depends(auth_dependency)):
    """
    首页
    """
    return '<h2>Welcome to FastAPI Proxy Pool System</h2><p>High Performance Async API</p>'


@app.get("/random", response_class=PlainTextResponse)
async def get_proxy(
    key: Optional[str] = Query(None, description="指定代理池键"),
    _: bool = Depends(auth_dependency),
    redis_client = Depends(get_redis_client)
):
    """
    获取随机代理
    支持指定代理池，如果启用降级模式且指定池为空，会从通用池获取
    """
    try:
        if key:
            try:
                proxy = await redis_client.random(key)
                return proxy.string()
            except PoolEmptyException:
                if not PROXY_RAND_KEY_DEGRADED:
                    raise HTTPException(status_code=404, detail="No proxy available in specified pool")
        
        # 从默认池获取
        proxy = await redis_client.random()
        return proxy.string()
    
    except PoolEmptyException:
        raise HTTPException(status_code=404, detail="No proxy available")
    except Exception as e:
        logger.error(f"Error getting random proxy: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/all", response_class=PlainTextResponse)
async def get_proxy_all(
    key: Optional[str] = Query(None, description="指定代理池键"),
    _: bool = Depends(auth_dependency),
    redis_client = Depends(get_redis_client)
):
    """
    获取所有代理（0-100分）
    """
    try:
        proxies = await redis_client.all(key) if key else await redis_client.all()
        
        if not proxies:
            return ""
        
        return '\n'.join(str(proxy) for proxy in proxies)
    
    except Exception as e:
        logger.error(f"Error getting all proxies: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/valid", response_class=PlainTextResponse)
async def get_valid_proxies(
    key: Optional[str] = Query(None, description="指定代理池键"),
    _: bool = Depends(auth_dependency),
    redis_client = Depends(get_redis_client)
):
    """
    获取所有有效代理（100分）
    """
    try:
        # 获取所有100分的代理
        proxies = await redis_client.all(
            key if key else None,
            PROXY_SCORE_MAX,
            PROXY_SCORE_MAX
        )

        if not proxies:
            return ""

        return '\n'.join(str(proxy) for proxy in proxies)

    except Exception as e:
        logger.error(f"Error getting valid proxies: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/count", response_class=PlainTextResponse)
async def get_count(
    key: Optional[str] = Query(None, description="指定代理池键"),
    _: bool = Depends(auth_dependency),
    redis_client = Depends(get_redis_client)
):
    """
    获取代理数量
    """
    try:
        count = await redis_client.count(key) if key else await redis_client.count()
        return str(count)
    
    except Exception as e:
        logger.error(f"Error getting proxy count: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/valid/count", response_class=PlainTextResponse)
async def get_valid_count(
    key: Optional[str] = Query(None, description="指定代理池键"),
    _: bool = Depends(auth_dependency),
    redis_client = Depends(get_redis_client)
):
    """
    获取有效代理数量（100分）
    """
    try:
        # 获取100分代理的数量
        logger.info(f"Getting valid proxies with key={key}, score_min={PROXY_SCORE_MAX}, score_max={PROXY_SCORE_MAX}")
        proxies = await redis_client.all(
            redis_key=key,
            proxy_score_min=PROXY_SCORE_MAX,
            proxy_score_max=PROXY_SCORE_MAX
        )
        count = len(proxies) if proxies else 0
        logger.info(f"Found {count} valid proxies")
        return str(count)

    except Exception as e:
        logger.error(f"Error getting valid proxy count: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.get("/health", response_class=PlainTextResponse)
async def health_check():
    """健康检查接口"""
    try:
        redis_client = await get_async_redis_client()
        count = await redis_client.count()
        return f"OK - FastAPI Server v2.0.0 - {count} proxies"
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unavailable")


# 兼容性：保持原有的启动方式
if __name__ == '__main__':
    import uvicorn
    
    # 开发环境配置
    if IS_DEV:
        uvicorn.run(
            "fastapi_server:app",
            host=API_HOST,
            port=API_PORT,
            reload=True,
            log_level="debug"
        )
    else:
        # 生产环境配置
        uvicorn.run(
            app,
            host=API_HOST,
            port=API_PORT,
            workers=1,  # 单进程，因为我们使用异步
            log_level="info"
        )
