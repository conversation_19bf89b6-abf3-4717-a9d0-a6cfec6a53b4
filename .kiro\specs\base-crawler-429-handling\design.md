# 设计文档

## 概述

改进BaseCrawler类的fetch方法，增加对HTTP 429状态码的专门处理。当前的实现使用固定的2秒重试间隔，对于频率限制场景不够灵活。新设计将在基类中直接处理429状态码，使用递增延迟策略和Retry-After头部信息。

## 架构

### 当前架构问题
- 使用retrying库的固定延迟重试机制
- 只处理200状态码，其他状态码被忽略
- 没有针对429状态码的特殊处理
- 重试逻辑基于返回值是否为None，而不是HTTP状态码

### 新架构设计
- 保留retrying库用于一般错误重试
- 在fetch方法内部增加429状态码的专门处理逻辑
- 实现递增延迟策略
- 支持Retry-After头部解析

## 组件和接口

### 修改的组件

#### BaseCrawler.fetch方法
- **输入**: url, timeout, **kwargs
- **输出**: str (HTML内容) 或 None
- **新增功能**: 429状态码处理逻辑

#### 新增内部方法
- **_handle_429_response**: 处理429响应的专门方法
- **_parse_retry_after**: 解析Retry-After头部的方法
- **_calculate_429_delay**: 计算429重试延迟的方法

### 接口保持不变
- fetch方法的公共接口保持完全兼容
- 现有子类无需任何修改

## 数据模型

### 429处理参数（硬编码）
```python
MAX_429_RETRIES = 3  # 429错误最大重试次数
BASE_429_DELAY = 5   # 基础延迟时间（秒）
MAX_429_DELAY = 30   # 最大延迟时间（秒）
DELAY_MULTIPLIER = 2 # 延迟递增倍数
```

### Retry-After头部格式
- 数字格式: "5" (秒数)
- HTTP日期格式: "Wed, 21 Oct 2015 07:28:00 GMT"

## 错误处理

### 429错误处理流程
1. 检测到429状态码
2. 解析Retry-After头部（如果存在）
3. 计算延迟时间（Retry-After优先，否则使用递增策略）
4. 记录日志信息
5. 等待指定时间
6. 重试请求（最多3次）
7. 如果仍然失败，返回None

### 其他错误处理
- 保持现有的ConnectionError和ReadTimeout处理
- 保持现有的retrying装饰器逻辑
- 非200且非429状态码返回None

### 日志记录
- 记录429错误的发生
- 记录使用的延迟时间
- 记录重试次数
- 记录Retry-After头部信息（如果存在）

## 测试策略

### 单元测试
- 测试429状态码的处理逻辑
- 测试Retry-After头部解析
- 测试延迟计算算法
- 测试重试次数限制
- 测试向后兼容性

### 集成测试
- 测试与现有爬虫子类的兼容性
- 测试在实际网络环境中的表现
- 测试与retrying装饰器的协同工作

### 模拟测试
- 模拟返回429状态码的服务器
- 模拟不同的Retry-After头部格式
- 模拟网络超时和连接错误

## 实现细节

### 核心逻辑伪代码
```python
def fetch(self, url, timeout=5.0, **kwargs):
    for attempt in range(MAX_429_RETRIES + 1):
        try:
            response = requests.get(url, **kwargs)
            
            if response.status_code == 200:
                return response.text
            elif response.status_code == 429:
                if attempt < MAX_429_RETRIES:
                    delay = self._calculate_429_delay(response, attempt)
                    logger.warning(f"429 rate limit, waiting {delay}s")
                    time.sleep(delay)
                    continue
                else:
                    logger.error("Max 429 retries exceeded")
                    return None
            else:
                return None
                
        except (ConnectionError, ReadTimeout):
            return None
```

### 延迟计算策略
1. 优先使用Retry-After头部值
2. 如果没有Retry-After，使用递增延迟: 5s, 10s, 20s
3. 延迟时间不超过30秒上限

### 向后兼容性
- 保持fetch方法签名不变
- 保持返回值类型不变
- 保持异常处理行为不变
- 现有的retrying装饰器继续工作